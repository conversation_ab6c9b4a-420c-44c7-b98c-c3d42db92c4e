/**
 * Production-ready EditorPageV3 - Refactored with modular architecture
 * Clean, maintainable, and scalable implementation
 */

import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useLocation, useNavigate, useParams, Link } from 'react-router-dom';

// API base URL configuration
const API_BASE = import.meta.env.VITE_API_BASE_URL || '/api';
import { useEditorV3, ChatMessage } from '../hooks/useEditorV3';
import SeamlessProgressiveRenderer from '../components/ProgressiveRenderer';
import { SPAShell } from '../components/SPAShell';
import ErrorBoundary from '../components/Editor/ErrorBoundary';
import ModalDialogs from '../components/Editor/ModalDialogs';
import ImplementationModal from '../components/Editor/ImplementationModal';
import ShareButton from '../components/ShareButton';
// VERSIONING DISABLED: Commented out versioning-related imports
// import PrototypeVersionSwitcher from '../components/Editor/PrototypeVersionSwitcher';
import { previewEnhancement, PromptEnhancement } from '../services/promptEnhancementService';
import { createElementSelectorManager, ElementSelection, extractEditParameters } from '../services/elementSelectorService';
import EditModeControls from '../components/Editor/EditModeControls';
import { getSession, getPageList, getProjectList, updatePage, deletePage } from '../services/pageGenService';
// import { usePrototypeVersions } from '../hooks/usePrototypeVersions';
// import { usePrototypeVersioning } from '../hooks/usePrototypeVersioning';
//import VersioningDebugPanel from '../components/Debug/VersioningDebugPanel';
import { createPageWithCleanName, generatePageContentPrompt } from '../services/pageCreationService';
import type { PendingPageCreation } from '../types/pageCreation';
import { Bars3Icon } from '@heroicons/react/24/outline';
import { Sparkles } from 'lucide-react';
import { PromptInput, DeviceType } from '../components/shared/PromptInput';
import { PageSidebar, Page as SidebarPage } from '../components/shared/PageSidebar';
import { PlanDisplay } from '../components/PlanDisplay';
import styles from './EditorPageV3Refactored.module.css';
import '../utils/mobileResponsiveTest'; // Auto-run mobile responsive tests

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// ============================================================================
// TYPES
// ============================================================================

interface LocationState {
  prompt?: string;
  plan?: any;
  projectId?: number;
  sessionId?: string;
  initialGeneration?: boolean;
  loadExistingPage?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EditorPageV3Refactored() {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();

  // Get projectId from URL params or location state
  const urlProjectId = params.projectId ? parseInt(params.projectId, 10) : undefined;
  const { prompt, plan, projectId: stateProjectId, sessionId, initialGeneration, loadExistingPage } = (location.state as LocationState) || {};
  const projectId = urlProjectId || stateProjectId;

  // Use the custom hook for all editor functionality
  const { state, actions } = useEditorV3({ projectId, sessionId });

  // VERSIONING DISABLED: Commented out version switching state
  // const [isVersionSwitching, setIsVersionSwitching] = useState(false);

  // VERSIONING DISABLED: Commented out first version tracking
  // const [hasFirstVersion, setHasFirstVersion] = useState(false);

  // VERSIONING DISABLED: Commented out prototype version management
  /*
  const {
    versionLabels,
    currentVersionLabel,
    isLoading: isVersionLoading,
    error: versionError,
    switchToVersion,
    clearError: clearVersionError,
    refreshVersions
  } = usePrototypeVersions({
    prototypeId: projectId || null,
    onVersionChange: (version) => {
      console.log('🔄 Prototype version changed:', version);

      // Mark as version switching to prevent new version creation
      setIsVersionSwitching(true);

      // Update editor content with the selected version
      actions.setHtmlContent(version.html);
      actions.setStableIframeContent(version.html);

      // Clear version switching flag after content is set
      setTimeout(() => {
        setIsVersionSwitching(false);
        console.log('✅ Version switching completed');
      }, 1500);
    }
  });
  */

  // VERSIONING DISABLED: Commented out automatic prototype versioning
  /*
  const {
    isVersionCreationInProgress,
    cancelPendingVersionCreation
  } = usePrototypeVersioning({
    prototypeId: projectId || null,
    htmlContent: state.htmlContent,
    isGenerating: state.isGenerating,
    currentPageId: state.currentPageId,
    pages: state.pages,
    isVersionSwitching: isVersionSwitching,
    onVersionCreated: (versionId) => {
      console.log('🎉 New prototype version created:', versionId);
      // Mark that we now have versions and refresh the list
      setHasFirstVersion(true);
      refreshVersions();
    }
  });
  */

  // Debug streaming content changes
  useEffect(() => {
    console.log('🎬 EditorPageV3Refactored: Streaming content changed:', {
      isGenerating: state.isGenerating,
      hasStreamingContent: !!state.streamingContent,
      streamingContentLength: state.streamingContent?.length || 0,
      streamingContentPreview: state.streamingContent?.substring(0, 100) + '...',
      hasHtmlContent: !!state.htmlContent,
      htmlContentLength: state.htmlContent?.length || 0
    });
  }, [state.streamingContent, state.isGenerating, state.htmlContent]);

  // VERSIONING DISABLED: Commented out first version creation listener
  /*
  useEffect(() => {
    const handleFirstVersionCreated = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId } = event.detail;
      if (eventPrototypeId === projectId) {
        console.log('🎉 First version created, enabling version controls (readdy.ai style)');
        setHasFirstVersion(true);
        // Trigger version loading
        refreshVersions();
      }
    };

    window.addEventListener('firstVersionCreated', handleFirstVersionCreated as EventListener);

    return () => {
      window.removeEventListener('firstVersionCreated', handleFirstVersionCreated as EventListener);
    };
  }, [projectId, refreshVersions]);
  */

  // Debug state for streaming content
  const [showDebugPanel, setShowDebugPanel] = useState(false);

  // Test progressive rendering
  const [isTestingProgressive, setIsTestingProgressive] = useState(false);

  const testProgressiveRendering = async () => {
    console.log('🎬 Testing progressive rendering...');
    setIsTestingProgressive(true);
    actions.setStreamingContent(''); // Clear any existing content

    const testHtml = `
<div class="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
  <h1 class="text-2xl font-bold text-gray-900 mb-4">Progressive Rendering Test</h1>
  <p class="text-gray-600 mb-4">This content is being rendered progressively, element by element.</p>
  <div class="grid grid-cols-2 gap-4 mb-4">
    <div class="bg-blue-100 p-4 rounded">
      <h3 class="font-semibold text-blue-900">Feature 1</h3>
      <p class="text-blue-700">Smooth animations</p>
    </div>
    <div class="bg-green-100 p-4 rounded">
      <h3 class="font-semibold text-green-900">Feature 2</h3>
      <p class="text-green-700">Progressive loading</p>
    </div>
  </div>
  <div class="bg-yellow-100 p-4 rounded mb-4">
    <h3 class="font-semibold text-yellow-900">Feature 3</h3>
    <p class="text-yellow-700">No jitter or flashing</p>
  </div>
  <button class="bg-violet-600 text-white px-4 py-2 rounded hover:bg-violet-700">
    Test Button
  </button>
</div>
    `.trim();

    console.log('🧪 Starting progressive rendering test with streaming content');

    // Simulate streaming by progressively adding content
    const lines = testHtml.split('\n');
    let accumulatedContent = '';

    for (let i = 0; i < lines.length; i++) {
      accumulatedContent += lines[i] + '\n';
      console.log('🧪 Setting streaming content:', {
        lineIndex: i,
        totalLines: lines.length,
        accumulatedLength: accumulatedContent.length,
        currentLine: lines[i].trim()
      });
      actions.setStreamingContent(accumulatedContent);
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate streaming delay
    }

    console.log('🧪 Progressive rendering test completed');

    setTimeout(() => {
      setIsTestingProgressive(false);
      actions.setStreamingContent(''); // Clear after test
    }, 3000);
  };

  // Track if initial generation has been triggered to prevent duplicates
  const [hasTriggeredInitialGeneration, setHasTriggeredInitialGeneration] = useState(false);

  // Track page loading state
  const [isLoadingPage, setIsLoadingPage] = useState(false);
  const [loadingPageId, setLoadingPageId] = useState<string | null>(null);

  // Track generation progress for better user feedback
  const [generationProgress, setGenerationProgress] = useState<{
    isActive: boolean;
    startTime: number;
    message: string;
  } | null>(null);

  // View mode for generation preview
  const [generationViewMode, setGenerationViewMode] = useState<'preview' | 'code'>('preview');

  // View mode for content display
  const [viewMode, setViewMode] = useState<'preview' | 'code'>('preview');

  // Project pages state
  const [projectPages, setProjectPages] = useState<any[]>([]);
  const [isLoadingProjectPages, setIsLoadingProjectPages] = useState(false);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(sessionId || null);

  // New page creation state
  const [isCreatingNewPage, setIsCreatingNewPage] = useState(false);
  const [newPagePrompt, setNewPagePrompt] = useState('');
  const [showPlanReview, setShowPlanReview] = useState(false);
  const [generatedPlan, setGeneratedPlan] = useState<any>(null);
  const [isGeneratingPlan, setIsGeneratingPlan] = useState(false);
  const [generatedPageName, setGeneratedPageName] = useState<string>('');

  // Page management state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null);

  // Left pane view mode
  const [isLeftPaneCollapsed, setIsLeftPaneCollapsed] = useState(false);

  // Mobile responsive state
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [mobileActiveTab, setMobileActiveTab] = useState<'preview' | 'chat'>('preview');

  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      const newIsMobile = window.innerWidth < 768;
      setIsMobile(newIsMobile);

      // Hide left pane by default on mobile
      if (newIsMobile) {
        setIsLeftPaneCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);

    // Set initial state
    handleResize();

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Function to refresh project pages - using useCallback for stable reference
  const refreshProjectPages = useCallback(async () => {
    if (projectId) {
      setIsLoadingProjectPages(true);
      try {
        console.log('🔄 Calling getPageList API for project:', projectId);
        const response = await getPageList(projectId);
        if (response.sessions) {
          console.log('✅ Page list refreshed, found pages:', response.sessions.length);
          console.log('📄 Page list data:', response.sessions.map(p => ({ id: p.id, title: p.title, created_at: p.created_at })));
          setProjectPages(response.sessions);
        } else {
          console.warn('⚠️ No sessions in API response:', response);
        }
      } catch (error) {
        console.error('❌ Error refreshing project pages:', error);
      } finally {
        setIsLoadingProjectPages(false);
      }
    }
  }, [projectId]);

  // Handle page rename for shared PageSidebar component
  const handlePageRenameShared = async (pageId: number, newName: string) => {
    try {
      // Call API to rename page
      const response = await updatePage(pageId, newName);

      if (response.success) {
        // Update local state
        setProjectPages(prev => prev.map(page =>
          page.id === pageId ? { ...page, title: newName } : page
        ));

        console.log(`✅ Page renamed to "${newName}"`);
      } else {
        throw new Error('Failed to rename page');
      }
    } catch (error) {
      console.error('Error renaming page:', error);
      // You could add a toast notification here
      throw error; // Re-throw to let the component handle the error state
    }
  };

  // Handle page delete for shared PageSidebar component
  const handlePageDeleteShared = async (pageId: number) => {
    try {
      // Call API to delete page
      const response = await deletePage(pageId);

      if (response.success) {
        const wasCurrentPage = currentSessionId === pageId.toString();

        // Update local state - remove the deleted page
        const updatedPages = projectPages.filter(p => p.id !== pageId);
        setProjectPages(updatedPages);

        // ENHANCED: Smart page selection after deletion
        if (wasCurrentPage) {
          console.log('📄 Current page deleted, implementing smart fallback selection');

          if (updatedPages.length === 0) {
            // No pages left - clear content and show empty state
            console.log('📄 Last page deleted, showing empty state');
            setCurrentSessionId(null);
            actions.setHtmlContent('');
            actions.setStableIframeContent('');

            // Optionally show create new page UI
            setIsCreatingNewPage(true);
            setShowPlanReview(false);
            setNewPagePrompt('');
            setGeneratedPageName('');

            // Add feedback message for empty state
            actions.addMessage({
              role: 'assistant',
              content: `✅ Page deleted. No pages remaining - create a new page to get started.`,
              timestamp: new Date()
            });
          } else {
            // Auto-select next available page (preferably most recently created)
            console.log('📄 Auto-selecting next available page after deletion');

            // Sort remaining pages by creation date to get most recently created
            const sortedPages = [...updatedPages].sort((a, b) =>
              new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );
            const nextPage = sortedPages[0];

            console.log('📄 Auto-selecting page:', nextPage.title || `Page ${nextPage.id}`);

            // CRITICAL: Auto-select the next page and ensure visual feedback
            await handleProjectPageSelect(nextPage);

            // Add feedback message with correct page name
            actions.addMessage({
              role: 'assistant',
              content: `✅ Page deleted. Switched to "${nextPage.title || `Page ${nextPage.id}`}".`,
              timestamp: new Date()
            });
          }
        } else {
          // Page deleted but wasn't current - just show success message
          console.log('✅ Page deleted successfully (was not current page)');
          actions.addMessage({
            role: 'assistant',
            content: `✅ Page deleted successfully.`,
            timestamp: new Date()
          });
        }
      } else {
        throw new Error('Failed to delete page');
      }
    } catch (error) {
      console.error('Error deleting page:', error);
      // You could add a toast notification here
      throw error; // Re-throw to let the component handle the error state
    }
  };

  // Element selector state for ChatInterface
  const [elementSelectorActive, setElementSelectorActive] = useState(false);

  // Prompt enhancement state
  const [enhancement, setEnhancement] = useState<PromptEnhancement | null>(null);
  const [showEnhancement, setShowEnhancement] = useState(false);
  const [isEnhancing, setIsEnhancing] = useState(false);
  const [originalMessage, setOriginalMessage] = useState<string>(''); // Store original since input is cleared

  // Element selector state (modular)
  const [isEditModeActive, setIsEditModeActive] = useState(false);
  const [selectedElement, setSelectedElement] = useState<ElementSelection | null>(null);
  const spaShellRef = useRef<HTMLDivElement>(null);
  const [elementSelectorManager] = useState(() =>
    createElementSelectorManager(
      (selection) => setSelectedElement(selection),
      (isActive) => setIsEditModeActive(isActive),
      undefined, // no iframe ref for SPAShell mode
      spaShellRef, // container ref for direct DOM access
      'direct' // use direct DOM mode
    )
  );



  // Additional state for robust UX
  const [showPageCreationDialog, setShowPageCreationDialog] = useState(false);
  const [pendingPageCreation, setPendingPageCreation] = useState<PendingPageCreation | null>(null);
  const [showLinkingDialog, setShowLinkingDialog] = useState(false);
  const [linkingProgress, setLinkingProgress] = useState<{
    current: number;
    total: number;
    currentPage: string;
  } | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);

  // SPAShell integration state
  const [useSPAMode, setUseSPAMode] = useState(true); // SPA mode always enabled (UI controls hidden)
  const [spaEditMode, setSpaEditMode] = useState(false); // SPAShell edit mode

  // Project selection state
  const [showProjectSelector, setShowProjectSelector] = useState(!projectId);
  const [availableProjects, setAvailableProjects] = useState<any[]>([]);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);

  // ============================================================================
  // INITIALIZATION EFFECTS
  // ============================================================================

  // Track if we've already shown the linking suggestion
  const [hasShownLinkingSuggestion, setHasShownLinkingSuggestion] = useState(false);

  // Simplified check for 2+ pages that need linking
  const checkIfPagesNeedLinking = (pages: any[]) => {
    const pagesWithContent = pages.filter(p => p.content && p.content.length > 50);
    // For simplicity: if we have 2+ pages, assume they need linking
    return pagesWithContent.length >= 2;
  };

  // Watch for page updates and auto-link when we have 2+ pages (with proper guards)
  useEffect(() => {
    const needsLinking = checkIfPagesNeedLinking(state.pages);

    // DISABLED: Auto-linking causes multiple popups and blinking
    // User can manually link pages using the Link Pages button
    if (false && needsLinking && !hasShownLinkingSuggestion && !showLinkingDialog && !linkingProgress) {
      console.log('🔗 Auto-linking disabled to prevent multiple popups');
      setHasShownLinkingSuggestion(true);
      handleLinkPages(); // Link immediately without dialog
    }
  }, [state.pages, hasShownLinkingSuggestion, showLinkingDialog, linkingProgress]);

  // Handle initial generation from plan page
  useEffect(() => {
    console.log('🔍 Initial generation check:', {
      initialGeneration,
      hasPrompt: !!prompt,
      hasHtmlContent: !!state.htmlContent,
      hasTriggeredInitialGeneration,
      shouldTrigger: false
    });

    if (initialGeneration && prompt && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('🚀 Starting initial generation from plan page (ONCE)');
      console.log('📋 Plan data structure:', plan ? Object.keys(plan) : 'No plan');
      console.log('💬 Original prompt:', prompt);
      console.log('🔍 Plan sections:', plan?.sections?.length || 0);
      console.log('🔍 Plan features:', plan?.features?.length || 0);

      // Mark as triggered to prevent duplicates
      setHasTriggeredInitialGeneration(true);

      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      actions.addMessage(userMessage);

      // Add plan message if we have a plan
      if (plan) {
        let planData;
        try {
          // Parse plan if it's a string
          planData = typeof plan === 'string' ? JSON.parse(plan) : plan;
        } catch (e) {
          planData = plan;
        }

        const planContent = formatPlanForDisplay(planData);
        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        actions.addMessage(planMessage);
      }

      // Create comprehensive prompt using BOTH original prompt AND plan data
      let comprehensivePrompt = prompt;

      if (plan && typeof plan === 'object') {
        // Build detailed prompt from plan data
        comprehensivePrompt = `${prompt}

📋 **DETAILED IMPLEMENTATION PLAN:**

🎯 **Project Overview:**
${plan.overview || 'Create a professional, modern design'}

🏗️ **Implementation Requirements:**`;

        // Add each section from the plan
        if (plan.sections && Array.isArray(plan.sections)) {
          plan.sections.forEach((section: any, index: number) => {
            comprehensivePrompt += `

${index + 1}. **${section.title}**
   ${section.description}`;

            if (section.details && Array.isArray(section.details)) {
              section.details.forEach((detail: string) => {
                comprehensivePrompt += `
   • ${detail}`;
              });
            }
          });
        }

        // Add features
        if (plan.features && Array.isArray(plan.features)) {
          comprehensivePrompt += `

✨ **Key Features to Implement:**`;
          plan.features.forEach((feature: string) => {
            comprehensivePrompt += `
• ${feature}`;
          });
        }

        // Add accessibility requirements
        if (plan.accessibility && Array.isArray(plan.accessibility)) {
          comprehensivePrompt += `

♿ **Accessibility Requirements:**`;
          plan.accessibility.forEach((item: string) => {
            comprehensivePrompt += `
• ${item}`;
          });
        }
      }

      // Add modal-ready instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🎯 Using comprehensive prompt with plan data:', comprehensivePrompt.substring(0, 200) + '...');
      console.log('📏 Final prompt length:', comprehensivePrompt.length);
      console.log('🔍 Plan sections included:', plan?.sections?.length || 0);
      console.log('🔍 Plan features included:', plan?.features?.length || 0);

      // Start generation with comprehensive prompt
      actions.generateFromPrompt(comprehensivePrompt);
    }
  }, [initialGeneration, prompt, state.htmlContent, plan, hasTriggeredInitialGeneration]);

  // Listen for navigation messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NAVIGATE_TO_PAGE') {
        const { pageId, pageName } = event.data;
        console.log('🔗 Navigation message received:', { pageId, pageName });

        // Debug: Check if this is a known page name
        const existingPageNames = state.pages.map(p => p.name);
        console.log('🔗 Existing pages:', existingPageNames);
        console.log('🔗 Clicked page name:', pageName);

        // Handle navigation click
        handleNavigationClick({
          textContent: pageName,
          isNavigation: true
        });
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [state.pages]);

  // Load existing page content when sessionId is provided
  useEffect(() => {
    if (loadExistingPage && sessionId && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('📄 Loading existing page content for session:', sessionId);

      const loadPageContent = async () => {
        setIsLoadingPage(true);

        try {
          const response = await getSession(sessionId);
          if (response.success && response.session) {
            console.log('✅ Page content loaded successfully');
            console.log('🔧 Page HTML length:', response.session.page_html?.length);
            console.log('🔧 Page HTML starts with:', response.session.page_html?.substring(0, 100));
            console.log('🔧 Page HTML includes userDashboard:', response.session.page_html?.includes('userDashboard'));

            // Set the HTML content in the editor
            actions.setHtmlContent(response.session.page_html);
            actions.setStableIframeContent(response.session.page_html);

            console.log('🔧 Content set in state, checking state values...');
            console.log('🔧 State htmlContent length:', state.htmlContent?.length);
            console.log('🔧 State stableIframeContent length:', state.stableIframeContent?.length);

            // Add a welcome message
            actions.addMessage({
              role: 'assistant',
              content: `📄 Loaded existing page. You can now edit this page or ask me to make changes.`,
              timestamp: new Date()
            });

            // Mark as triggered to prevent other loading attempts
            setHasTriggeredInitialGeneration(true);

            // Update current session ID for UI highlighting
            setCurrentSessionId(sessionId);
          }
        } catch (error) {
          console.error('❌ Error loading page content:', error);
          actions.addMessage({
            role: 'assistant',
            content: `❌ Failed to load page content. Please try again or create a new page.`,
            timestamp: new Date()
          });
        } finally {
          setIsLoadingPage(false);
        }
      };

      loadPageContent();
    }
  }, [loadExistingPage, sessionId, state.htmlContent, hasTriggeredInitialGeneration, actions]);

  // Load available projects when no project is specified
  useEffect(() => {
    if (!projectId) {
      console.log('📄 No project specified, loading available projects');

      const loadProjects = async () => {
        setIsLoadingProjects(true);
        try {
          const response = await getProjectList(1, 50); // Load first 50 projects
          if (response.projects) {
            console.log('✅ Available projects loaded:', response.projects.length);
            setAvailableProjects(response.projects);
          }
        } catch (error) {
          console.error('❌ Error loading projects:', error);
        } finally {
          setIsLoadingProjects(false);
        }
      };

      loadProjects();
    }
  }, [projectId]);

  // Load project pages when component mounts or projectId changes
  useEffect(() => {
    console.log('🔍 Project pages loading check:', {
      projectId,
      hasHtmlContent: !!state.htmlContent,
      hasStableContent: !!state.stableIframeContent,
      hasTriggeredInitialGeneration,
      shouldLoadPages: !!projectId,
      hasLocationState: !!(location.state),
      loadExistingPage,
      sessionId
    });

    if (projectId) {
      console.log('📄 Loading project pages for project:', projectId);

      const loadProjectPages = async () => {
        setIsLoadingProjectPages(true);
        try {
          const response = await getPageList(projectId);
          if (response.sessions) {
            console.log('✅ Project pages loaded:', response.sessions.length);
            setProjectPages(response.sessions);

            // Auto-expand pages panel when project pages are loaded
            if (response.sessions.length > 0) {
              setIsLeftPaneCollapsed(false);

              // ENHANCED: Auto-select most recently created page when project is opened
              // Only if no specific page is being loaded and no generation has been triggered
              if (!currentSessionId && !state.htmlContent && !state.stableIframeContent && !hasTriggeredInitialGeneration && !loadExistingPage) {
                console.log('📄 Auto-selecting most recently created page as default active page');

                // Sort pages by creation date to get the most recently created
                const sortedPages = [...response.sessions].sort((a, b) =>
                  new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                );
                const mostRecentPage = sortedPages[0];

                // Mark as triggered to prevent other loading attempts
                setHasTriggeredInitialGeneration(true);

                // Auto-select and load the most recent page
                console.log('📄 Auto-selecting page:', mostRecentPage.title || `Page ${mostRecentPage.id}`);
                handleProjectPageSelect(mostRecentPage);
              } else {
                console.log('📄 Pages available, current page already selected or generation already triggered');
              }
            } else {
              // If no pages exist, show empty state
              console.log('📄 No pages found, showing empty state');
              // Clear any existing content to ensure clean empty state
              if (state.htmlContent || state.stableIframeContent) {
                actions.setHtmlContent('');
                actions.setStableIframeContent('');
              }
              setCurrentSessionId(null);
            }
          }
        } catch (error) {
          console.error('❌ Error loading project pages:', error);
        } finally {
          setIsLoadingProjectPages(false);
        }
      };

      loadProjectPages();
    }
  }, [projectId]);

  // Listen for auto-save completion events and refresh page list
  useEffect(() => {
    const handlePageSaved = async (event: CustomEvent) => {
      const saveData = event.detail;
      console.log('🔄 Page saved event received:', saveData);

      // CRITICAL: Add small delay then refresh page list to ensure new page appears in sidebar
      console.log('🔄 Adding small delay before refreshing page list to ensure API consistency');
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay
      console.log('🔄 Refreshing page list after page save');
      await refreshProjectPages();

      // ENHANCED: Auto-select newly created page after generation completes
      if (saveData.sessionId) {
        console.log('🔄 Auto-selecting newly created page:', saveData.pageTitle || 'New Page');

        // CRITICAL: Set the new page as current for instant visual feedback
        const newPageId = saveData.sessionId.toString();
        setCurrentSessionId(newPageId);

        // Load the new page content
        try {
          const response = await getSession(newPageId);
          if (response.success && response.session) {
            // CRITICAL: Set content immediately for instant display
            actions.setHtmlContent(response.session.page_html);
            actions.setStableIframeContent(response.session.page_html);

            // Check if page exists in state, if not add it, then switch to it
            const existingPageInState = state.pages.find(p => p.id === newPageId);
            if (existingPageInState) {
              // Page exists, just switch to it
              actions.switchToPage(newPageId);
            } else {
              // Page doesn't exist in state, add it
              const pageForState = {
                id: newPageId,
                name: saveData.pageTitle || 'New Page',
                content: response.session.page_html,
                isActive: false // No active flags needed, just use currentSessionId for UI
              };
              actions.addPage(pageForState);
              // CRITICAL: Switch to the newly added page to make it active
              actions.switchToPage(newPageId);
            }

            // Add success message
            actions.addMessage({
              role: 'assistant',
              content: `✅ Page "${saveData.pageTitle || 'New Page'}" has been automatically saved and is now active! You can now edit this page or create another one.`,
              timestamp: new Date()
            });

            console.log('✅ New page auto-selection completed:', {
              pageId: newPageId,
              pageTitle: saveData.pageTitle,
              currentSessionId: newPageId,
              stateCurrentPageId: state.currentPageId,
              pageListLength: projectPages.length
            });
          }
        } catch (error) {
          console.error('❌ Error loading newly saved page:', error);
          // Still try to refresh page list on error
          console.log('🔄 Fallback page list refresh after error');
          await refreshProjectPages();
        }
      }
    };

    const handlePageSaveError = (event: CustomEvent) => {
      const errorData = event.detail;
      console.error('❌ Page save error event received:', errorData);

      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to auto-save page: ${errorData.error}. You may need to save manually.`,
        timestamp: new Date()
      });
    };

    const handlePageSaveSkipped = (event: CustomEvent) => {
      const skipData = event.detail;
      console.warn('⚠️ Page save skipped event received:', skipData);

      actions.addMessage({
        role: 'assistant',
        content: `⚠️ Page auto-save was skipped: ${skipData.reason}. You may need to save manually.`,
        timestamp: new Date()
      });
    };

    const handleGenerationCompleted = async (event: CustomEvent) => {
      const { pageTitle, generatedHTML, fallback } = event.detail;
      console.log('🔄 Generation completed event received (fallback mode):', { pageTitle, fallback, hasHTML: !!generatedHTML });

      if (fallback) {
        // This is a fallback scenario - generation completed but no auto-save event received
        // Refresh the page list to check if the page was actually created
        await refreshProjectPages();

        // Add a delay and check again to give the backend time to save
        setTimeout(async () => {
          console.log('🔄 Performing delayed page list refresh for fallback scenario');
          await refreshProjectPages();

          // Look for a recently created page that matches the title
          const recentPages = projectPages.filter(page => {
            const pageCreatedTime = new Date(page.created_at).getTime();
            const timeDiff = Date.now() - pageCreatedTime;
            return timeDiff < 60000; // Created within the last minute
          });

          if (recentPages.length > 0) {
            const latestPage = recentPages[recentPages.length - 1];
            console.log('🔄 Found recently created page in fallback mode:', latestPage);

            // Switch to the newly found page
            setCurrentSessionId(latestPage.id);

            try {
              const response = await getSession(latestPage.id);
              if (response.success && response.session) {
                actions.setHtmlContent(response.session.page_html);
                actions.setStableIframeContent(response.session.page_html);

                // Add page to state if it doesn't exist and switch to it
                const existingPageInState = state.pages.find(p => p.id === latestPage.id);
                if (!existingPageInState) {
                  const pageForState = {
                    id: latestPage.id,
                    name: latestPage.title || pageTitle || 'New Page',
                    content: response.session.page_html,
                    isActive: false
                  };
                  actions.addPage(pageForState);
                }
                // Switch to the page to make it active
                actions.switchToPage(latestPage.id);

                actions.addMessage({
                  role: 'assistant',
                  content: `✅ Found your page "${latestPage.title || pageTitle}"! It was successfully created and is now available.`,
                  timestamp: new Date()
                });
              }
            } catch (error) {
              console.error('❌ Error loading fallback page:', error);
            }
          } else {
            actions.addMessage({
              role: 'assistant',
              content: `⚠️ Page generation completed but the page was not found in the database. Please try creating the page again.`,
              timestamp: new Date()
            });
          }
        }, 5000); // 5 second delay for fallback check
      }
    };

    // Add event listeners
    window.addEventListener('pageSaved', handlePageSaved as EventListener);
    window.addEventListener('pageSaveError', handlePageSaveError as EventListener);
    window.addEventListener('pageSaveSkipped', handlePageSaveSkipped as EventListener);
    window.addEventListener('generationCompleted', handleGenerationCompleted as EventListener);

    // Cleanup
    return () => {
      window.removeEventListener('pageSaved', handlePageSaved as EventListener);
      window.removeEventListener('pageSaveError', handlePageSaveError as EventListener);
      window.removeEventListener('pageSaveSkipped', handlePageSaveSkipped as EventListener);
      window.removeEventListener('generationCompleted', handleGenerationCompleted as EventListener);
    };
  }, [projectId]); // FIXED: Removed frequently changing dependencies to prevent event listener re-registration

  // Track projectPages changes for debugging
  useEffect(() => {
    console.log('📄 projectPages state updated:', {
      pageCount: projectPages.length,
      pages: projectPages.map(p => ({ id: p.id, title: p.title })),
      currentSessionId,
      timestamp: new Date().toISOString()
    });
  }, [projectPages, currentSessionId]);

  // ENHANCED: Bidirectional synchronization between currentSessionId and state.currentPageId
  // This ensures single source of truth for page selection state with proper type handling
  useEffect(() => {
    // Convert both to strings for consistent comparison (PageSidebar expects string/number, state uses string)
    const statePageId = state.currentPageId ? state.currentPageId.toString() : null;
    const sessionId = currentSessionId ? currentSessionId.toString() : null;

    if (statePageId && statePageId !== sessionId) {
      console.log('🔄 Synchronizing currentSessionId with state.currentPageId:', {
        stateCurrentPageId: statePageId,
        currentSessionId: sessionId,
        action: 'updating currentSessionId to match state'
      });
      setCurrentSessionId(statePageId);
    } else if (sessionId && sessionId !== statePageId) {
      // If currentSessionId is set but state doesn't match, this is expected during page selection
      // The state will be updated by the page selection logic (handleProjectPageSelect -> actions.switchToPage)
      console.log('🔄 currentSessionId set, state will be updated by page selection logic:', {
        currentSessionId: sessionId,
        stateCurrentPageId: statePageId,
        action: 'waiting for state update via switchToPage'
      });
    }
  }, [state.currentPageId, currentSessionId]);

  // Handle post-generation content display
  // When generation completes, ensure the final content is properly displayed
  useEffect(() => {
    // Only handle this when generation has just completed
    if (!state.isGenerating && state.htmlContent && currentSessionId) {
      console.log('🎯 Post-generation content display check:', {
        isGenerating: state.isGenerating,
        hasHtmlContent: !!state.htmlContent,
        hasStableContent: !!state.stableIframeContent,
        currentSessionId,
        htmlContentLength: state.htmlContent.length
      });

      // Ensure stable content is set for proper display
      if (!state.stableIframeContent && state.htmlContent) {
        console.log('🔄 Setting stable content from HTML content for display');
        actions.setStableIframeContent(state.htmlContent);
      }
    }
  }, [state.isGenerating, state.htmlContent, state.stableIframeContent, currentSessionId, actions]);

  // Track generation progress and provide user feedback
  // Only show progress for actual content generation, not for loading existing pages
  useEffect(() => {
    if (state.isGenerating && !generationProgress && !isLoadingPage) {
      // Start tracking generation progress only for actual generation, not page loading
      console.log('🔄 Starting generation progress tracking');
      setGenerationProgress({
        isActive: true,
        startTime: Date.now(),
        message: 'Starting page generation...'
      });
    } else if (!state.isGenerating && generationProgress) {
      // Generation completed
      console.log('🔄 Generation completed, clearing progress indicator');
      setGenerationProgress(null);
    }
  }, [state.isGenerating, generationProgress, isLoadingPage]);

  // Safety check: Reset generation progress if it's been active too long without state.isGenerating
  useEffect(() => {
    if (generationProgress?.isActive && !state.isGenerating) {
      const elapsed = Date.now() - generationProgress.startTime;
      // If progress has been active for more than 5 seconds without state.isGenerating, reset it
      if (elapsed > 5000) {
        console.log('🔄 Resetting stuck generation progress indicator');
        setGenerationProgress(null);
      }
    }
  }, [generationProgress, state.isGenerating]);

  // Reset generation progress when loading pages (not generating)
  useEffect(() => {
    if (isLoadingPage && generationProgress) {
      console.log('🔄 Page loading detected, clearing generation progress');
      setGenerationProgress(null);
    }
  }, [isLoadingPage]); // Remove generationProgress from dependency array to prevent infinite loop

  // Cleanup generation progress on component unmount
  useEffect(() => {
    return () => {
      if (generationProgress) {
        console.log('🔄 Component unmounting, clearing generation progress');
        setGenerationProgress(null);
      }
    };
  }, []); // Empty dependency array - cleanup should only run on unmount

  // Update progress messages based on elapsed time
  useEffect(() => {
    if (!generationProgress?.isActive) return;

    // Store the initial startTime to avoid dependency changes
    const initialStartTime = generationProgress.startTime;

    const updateProgressMessage = () => {
      const elapsed = Date.now() - initialStartTime;
      let message = 'Generating your page...';

      if (elapsed > 30000) { // 30 seconds
        message = 'Generation is taking longer than usual, please wait...';
      }
      if (elapsed > 60000) { // 1 minute
        message = 'Still working on your page, this may take a few more moments...';
      }
      if (elapsed > 120000) { // 2 minutes
        message = 'Complex generation in progress, almost done...';
      }
      if (elapsed > 180000) { // 3 minutes
        message = 'Generation is taking longer than expected, but still processing...';
      }

      setGenerationProgress(prev => prev ? { ...prev, message } : null);
    };

    // Update message immediately and then every 10 seconds
    updateProgressMessage();
    const interval = setInterval(updateProgressMessage, 10000);

    return () => clearInterval(interval);
  }, [generationProgress?.isActive]); // Fixed: Only depend on isActive, not startTime



  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================

  // Handle project selection
  const handleProjectSelect = (selectedProjectId: number) => {
    console.log('📄 Project selected:', selectedProjectId);
    // Navigate to the same route with the project ID
    navigate(`/editor-v3-refactored/${selectedProjectId}`);
  };

  // Enhancement handlers
  const handleUseEnhanced = async () => {
    if (enhancement) {
      console.log('🎯 User chose enhanced prompt');
      setShowEnhancement(false);
      setEnhancement(null);

      // Proceed with enhanced prompt
      await proceedWithSubmit(enhancement.enhancedPrompt);
      setOriginalMessage(''); // Clear after use
    }
  };

  const handleCloseEnhancement = async () => {
    console.log('🔄 User chose original prompt');
    setShowEnhancement(false);
    setEnhancement(null);

    // Proceed with original prompt (stored since input was cleared)
    if (originalMessage.trim()) {
      await proceedWithSubmit(originalMessage.trim());
      setOriginalMessage(''); // Clear after use
    }
  };

  const handleChatSubmit = async (message: string) => {
    console.log('🔥 handleChatSubmit called with message:', message);

    // Clear input immediately (normal chat behavior)
    actions.clearInput();

    // Try enhancement first (only for edits)
    const enhancementShown = await triggerEnhancement(message);
    if (enhancementShown) {
      console.log('🔧 Enhancement shown, waiting for user choice');
      return; // Don't proceed yet, wait for user to choose enhanced/original
    }

    // Proceed with normal flow
    await proceedWithSubmit(message);
  };

  const proceedWithSubmit = async (message: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    actions.addMessage(userMessage);
    // Input already cleared in handleChatSubmit

    // Determine if this is an edit or new generation
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    console.log('🔥 Content check:', {
      hasHtmlContent: state.htmlContent.length > 0,
      hasStableContent: state.stableIframeContent.length > 0,
      hasContent,
      willEdit: hasContent,
      willGenerate: !hasContent
    });

    if (hasContent) {
      console.log('🔥 Calling editContent for existing content');

      // Extract element selector parameters for targeted editing
      const editParams = extractEditParameters(selectedElement);

      await actions.editContent(
        message,
        [], // no additional messages
        editParams.elementSelector,
        undefined, // implementationType - not needed for chat edits
        selectedElement // pass the full selectedElement data
      );

      // After successful edit, reload the page content from database to ensure consistency
      if (currentSessionId) {
        console.log('🔄 Reloading page content after successful edit...');
        const reloadSuccess = await actions.reloadCurrentPage(currentSessionId);
        if (reloadSuccess) {
          console.log('✅ Page content reloaded successfully after edit');
        } else {
          console.warn('⚠️ Failed to reload page content after edit');
        }
      }
    } else {
      console.log('🔥 Calling generateFromPrompt for new content');
      await actions.generateFromPrompt(message);
    }
  };



  // Enhanced element click handler that works with both PreviewPanel and SPAShell
  const handleElementClick = async (element: any) => {
    console.log('🔥 Enhanced element click handler:', element);

    // Handle navigation clicks
    if (element.isNavigation) {
      console.log('🔥 Navigation detected, handling navigation click');
      handleNavigationClick(element);
      return;
    }

    // Handle interactive elements that need implementation
    if ((element.implementationType && element.implementationReason) || element.isInteractive) {
      console.log('🔥 Interactive element needs implementation:', element.implementationReason);

      // Convert element data to proper ElementInfo format
      const elementInfo = {
        selector: element.selector || '',
        tagName: element.tagName || '',
        textContent: element.textContent || '',
        attributes: element.attributes || {},
        isNavigation: element.isNavigation || false,
        isInteractive: element.isInteractive || true,
        implementationType: element.implementationType,
        implementationReason: element.implementationReason,
        outerHTML: element.outerHTML, // Ensure outerHTML is preserved
        intentData: element.intentData
      };

      console.log('🔍 Converted element info:', {
        hasOuterHTML: !!elementInfo.outerHTML,
        outerHTMLLength: elementInfo.outerHTML?.length || 0,
        outerHTMLPreview: elementInfo.outerHTML?.substring(0, 100) + '...'
      });

      // Set selected element and show implementation modal
      actions.setSelectedElement(elementInfo);
      actions.setShowImplementModal(true);
      return;
    }

    console.log('🔥 Element does not need implementation, ignoring');
  };

  const handleNavigationClick = async (element: any) => {
    const rawPageName = element.textContent.trim();

    try {
      // Create page with clean name using the new service
      const result = await createPageWithCleanName(
        { prompt: rawPageName },
        'navigation-link'
      );

      if (!result.success) {
        throw new Error(result.error || 'Failed to create page');
      }

      const { pageName, pageId } = result;
      console.log('🔥 Processing navigation click:', { rawPageName, pageName, pageId });

      // Smart page matching: handle both "Reports" and "Reports Page" scenarios
      const existingPage = state.pages.find(p => {
        const normalizedPageName = pageName!.toLowerCase().trim();
        const normalizedExistingName = p.name.toLowerCase().trim();

        // Direct matches
        if (p.id === pageId || normalizedExistingName === normalizedPageName) {
          return true;
        }

        // Handle "Reports" → "Reports Page" mismatch
        if (normalizedExistingName === normalizedPageName + ' page') {
          return true;
        }

        // Handle "Reports Page" → "Reports" mismatch
        if (normalizedPageName === normalizedExistingName + ' page') {
          return true;
        }

        // Handle page ID matches (e.g., "reports" matches "reports-page")
        const clickedPageId = pageId;
        const existingPageId = p.id;
        const clickedPageIdWithPage = `${pageId}-page`;

        if (existingPageId === clickedPageId || existingPageId === clickedPageIdWithPage || clickedPageId === existingPageId) {
          return true;
        }

        return false;
      });

      if (existingPage) {
        console.log('🔥 Page already exists, switching to:', existingPage);
        actions.switchToPage(existingPage.id);

        // Add feedback message
        actions.addMessage({
          role: 'assistant',
          content: `✅ Switched to "${existingPage.name}" page`,
          timestamp: new Date()
        });
      } else {
        console.log('🔥 Page does not exist, showing creation dialog');

        // Show confirmation dialog instead of creating immediately
        const pendingCreation: PendingPageCreation = {
          pageName: pageName!,
          pageId: pageId!,
          source: 'navigation-link',
          originalPrompt: rawPageName
        };
        setPendingPageCreation(pendingCreation);
        setShowPageCreationDialog(true);
      }
    } catch (error) {
      console.error('❌ Error processing navigation click:', error);

      // Fallback to basic page creation
      const pageName = rawPageName;
      const pageId = pageName.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
      const pendingCreation: PendingPageCreation = {
        pageName,
        pageId,
        source: 'navigation-link',
        originalPrompt: rawPageName
      };
      setPendingPageCreation(pendingCreation);
      setShowPageCreationDialog(true);
    }
  };

  const confirmPageCreation = async () => {
    if (!pendingPageCreation) {
      console.log('❌ No pending page creation');
      return;
    }

    const { pageName, pageId, source } = pendingPageCreation;
    console.log('🚀 Confirming page creation:', { pageName, pageId, source });

    // Close dialog
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    try {
      // Clear current page selection to prepare for new page creation
      setCurrentSessionId(null);
      actions.setHtmlContent('');
      actions.setStableIframeContent('');

      // Add feedback message
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating new "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate content using the same pattern as main page creation
      // This will auto-save the page after HTML generation completes
      const prompt = generatePageContentPrompt(pageName, state.pages);
      console.log('🎯 Generating content with prompt:', prompt.substring(0, 100) + '...');

      try {
        //        await actions.generateFromPrompt(prompt, pageName);
        console.log('✅ Page generation completed');

        // Page will be automatically refreshed and switched when auto-save completes
        // via the pageSaved event listener

        console.log('🔗 Page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Page generation failed:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to generate content for "${pageName}". Please try again.`,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('❌ Error creating page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  const cancelPageCreation = () => {
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    actions.addMessage({
      role: 'assistant',
      content: `❌ Page creation cancelled`,
      timestamp: new Date()
    });
  };



  // Handle project page selection (load page content directly)
  const handleProjectPageSelect = async (page: any) => {
    console.log('📄 Project page selected:', page);
    console.log('📄 Current state before selection:', {
      currentSessionId,
      stateCurrentPageId: state.currentPageId,
      statePagesCount: state.pages.length
    });

    // Don't reload if it's the same page
    if (currentSessionId === page.id) {
      console.log('📄 Same page selected, ignoring');
      return;
    }

    // Hide new page creation UI when selecting an existing page
    setIsCreatingNewPage(false);
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');
    setGeneratedPlan(null);

    // Set loading state for this specific page
    setIsLoadingPage(true);
    setLoadingPageId(page.id);

    try {
      // Load the page content
      const response = await getSession(page.id);
      if (response.success && response.session) {
        console.log('✅ Page content loaded successfully');

        // Validate that we have actual content
        if (!response.session.page_html || response.session.page_html.trim().length === 0) {
          console.warn('⚠️ Page loaded but has no content');
          actions.addMessage({
            role: 'assistant',
            content: `⚠️ Page "${page.title || `Page ${page.id}`}" appears to be empty. You may need to regenerate its content.`,
            timestamp: new Date()
          });
        }

        // CRITICAL: Update current session ID FIRST for immediate UI highlighting
        const pageIdString = page.id.toString();
        console.log('🔧 Setting current session ID for immediate visual selection:', pageIdString);
        setCurrentSessionId(pageIdString);

        // Update editor state with the page content
        const pageContent = response.session.page_html || '';

        // CRITICAL: Reset ALL generation-related state first to ensure clean state
        console.log('🔧 Resetting all generation state for existing page load');
        actions.setIsGenerating(false);
        actions.setStreamingContent('');

        // CRITICAL: Set content immediately in editor state for instant display (no progressive rendering)
        console.log('🔧 Setting content immediately for instant display:', {
          contentLength: pageContent.length,
          contentPreview: pageContent.substring(0, 100) + '...'
        });
        actions.setHtmlContent(pageContent);
        actions.setStableIframeContent(pageContent);

        // Update the state.pages array and switch to the page
        const existingPage = state.pages.find(p => p.id === pageIdString);
        if (existingPage) {
          // Page exists in state, update its content and switch to it
          actions.updatePage(pageIdString, { content: pageContent });
          actions.switchToPage(pageIdString);
        } else {
          // Page doesn't exist in state, add it and switch to it
          const newPage = {
            id: pageIdString,
            name: page.title || `Page ${page.id}`,
            content: pageContent,
            isActive: false // No active flags needed, just use currentSessionId for UI
          };
          actions.addPage(newPage);
          actions.switchToPage(pageIdString);
        }

        console.log('🔧 Page selection state after update:', {
          currentSessionId: pageIdString,
          stateCurrentPageId: state.currentPageId,
          pageInState: !!state.pages.find(p => p.id === pageIdString)
        });

        console.log('🔍 Page selection completed:', {
          pageId: page.id,
          contentLength: pageContent.length,
          currentPageId: state.currentPageId,
          htmlContentLength: state.htmlContent?.length || 0,
          stableContentLength: state.stableIframeContent?.length || 0
        });

        // Add a message to show page switch
        actions.addMessage({
          role: 'assistant',
          content: `📄 Switched to "${page.title || `Page ${page.id}`}". You can now edit this page or ask me to make changes.`,
          timestamp: new Date()
        });

        console.log('✅ Page switched successfully');
        console.log('📄 Current state after selection:', {
          currentSessionId,
          stateCurrentPageId: state.currentPageId,
          statePagesCount: state.pages.length
        });
      } else {
        // Handle API error response
        console.error('❌ Failed to load page:', response);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to load page "${page.title || `Page ${page.id}`}". The page may have been deleted or there was a server error.`,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('❌ Error loading page content:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to load page content: ${errorMessage}. Please try again or refresh the page.`,
        timestamp: new Date()
      });
    } finally {
      setIsLoadingPage(false);
      setLoadingPageId(null);
    }
  };



  // Handle new page creation button click
  const handleCreateNewPage = () => {
    console.log('🆕 Creating new page - resetting all states');

    // Clear current page selection
    setCurrentSessionId(null);

    // Clear editor content
    actions.setHtmlContent('');
    actions.setStableIframeContent('');

    // Reset ALL plan-related states to ensure clean slate
    setShowPlanReview(false);
    setGeneratedPlan(null);
    setGeneratedPageName('');
    setIsGeneratingPlan(false);

    // Show new page creation mode
    setIsCreatingNewPage(true);
    setNewPagePrompt('');

    // Add welcome message
    actions.addMessage({
      role: 'assistant',
      content: `🚀 Ready to create a new page! Describe what kind of page you'd like to create and I'll generate it for you.`,
      timestamp: new Date()
    });
  };

  // Handle new page prompt submission - Generate plan first
  const handleNewPagePromptSubmit = async () => {
    if (!newPagePrompt.trim() || !projectId) return;

    setIsGeneratingPlan(true);

    try {
      // Use the already generated page name
      const pageName = generatedPageName || 'New Page';
      console.log('📄 Using generated page name:', pageName, 'from prompt:', newPagePrompt);

      // Add user message
      actions.addMessage({
        role: 'user',
        content: newPagePrompt,
        timestamp: new Date()
      });

      // Add assistant message about plan generation
      actions.addMessage({
        role: 'assistant',
        content: `🎯 Generating plan for "${pageName}" page...`,
        timestamp: new Date()
      });

      // Generate structured plan using the existing API
      const response = await fetch(`${API_BASE}/llm/v3/plan`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          prompt: newPagePrompt,
          deviceType: 'desktop' // Default to desktop for pages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.plan) {
          console.log('✅ Plan generated successfully:', result.plan);

          // Store the plan and show review
          setGeneratedPlan(result.plan);
          setShowPlanReview(true);

          // Add plan to chat
          actions.addMessage({
            role: 'assistant',
            content: `📋 Here's the plan for your "${pageName}" page. Review it and click "Generate Page" to proceed:

${formatPlanForDisplay(result.plan)}`,
            timestamp: new Date()
          });
        } else {
          throw new Error('No plan returned from API');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Plan generation API failed');
      }

    } catch (error) {
      console.error('❌ Error generating plan:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to generate plan. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setIsGeneratingPlan(false);
    }
  };

  // Handle plan approval and page generation
  const handleGeneratePageFromPlan = async () => {
    if (!generatedPlan || !newPagePrompt.trim()) {
      console.error('❌ Cannot generate page: missing plan or prompt', {
        hasGeneratedPlan: !!generatedPlan,
        hasNewPagePrompt: !!newPagePrompt.trim(),
        generatedPlan,
        newPagePrompt
      });
      return;
    }

    try {
      const pageName = generatedPageName || 'New Page';

      console.log('🚀 Starting page generation from plan:', {
        pageName,
        generatedPageName,
        newPagePrompt: newPagePrompt.substring(0, 100) + '...',
        projectId,
        hasGeneratedPlan: !!generatedPlan
      });

      // Immediately switch to code/preview mode by resetting plan review state
      setIsCreatingNewPage(false);
      setShowPlanReview(false);

      // Add assistant message about page creation
      actions.addMessage({
        role: 'assistant',
        content: `🚀 Creating "${pageName}" page based on the approved plan...`,
        timestamp: new Date()
      });

      // Create comprehensive prompt with plan data (like plan generation page)
      let comprehensivePrompt = `${newPagePrompt}

📋 **DETAILED PLAN TO IMPLEMENT:**

**Overview:** ${generatedPlan.overview || 'Create the requested page'}`;

      // Add sections if available
      if (generatedPlan.sections && Array.isArray(generatedPlan.sections)) {
        comprehensivePrompt += `

📄 **PAGE SECTIONS:**`;
        generatedPlan.sections.forEach((section: string) => {
          comprehensivePrompt += `
• ${section}`;
        });
      }

      // Add features if available
      if (generatedPlan.features && Array.isArray(generatedPlan.features)) {
        comprehensivePrompt += `

⚡ **KEY FEATURES:**`;
        generatedPlan.features.forEach((feature: string) => {
          comprehensivePrompt += `
• ${feature}`;
        });
      }

      // Add accessibility requirements if available
      if (generatedPlan.accessibility && Array.isArray(generatedPlan.accessibility)) {
        comprehensivePrompt += `

♿ **ACCESSIBILITY REQUIREMENTS:**`;
        generatedPlan.accessibility.forEach((item: string) => {
          comprehensivePrompt += `
• ${item}`;
        });
      }

      // Add interactive elements instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🚀 Calling generateFromPrompt with comprehensive prompt:', {
        originalPrompt: newPagePrompt.substring(0, 100) + '...',
        comprehensivePromptLength: comprehensivePrompt.length,
        pageTitle: pageName,
        projectId,
        planSections: generatedPlan.sections?.length || 0,
        planFeatures: generatedPlan.features?.length || 0
      });

      await actions.generateFromPrompt(comprehensivePrompt, pageName);

      console.log('✅ generateFromPrompt completed successfully');

      // Reset remaining new page creation state
      setNewPagePrompt('');
      setGeneratedPlan(null);

      // Page will be automatically refreshed and switched when auto-save completes
      // via the pageSaved event listener

    } catch (error) {
      console.error('❌ Error creating page:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to create page: ${errorMessage}. Please try again.`,
        timestamp: new Date()
      });
    }
  };

  // Handle plan rejection - go back to prompt editing
  const handleRejectPlan = () => {
    setShowPlanReview(false);
    setGeneratedPlan(null);

    actions.addMessage({
      role: 'assistant',
      content: `📝 Plan rejected. You can modify your prompt and try again.`,
      timestamp: new Date()
    });
  };

  // Cancel new page creation
  const handleCancelNewPage = () => {
    // Only set isCreatingNewPage to false if there are existing pages
    // If no pages exist, keep the prompt visible
    if (projectPages.length > 0) {
      setIsCreatingNewPage(false);
    }
    setNewPagePrompt('');
    setShowPlanReview(false);
    setGeneratedPageName('');

    actions.addMessage({
      role: 'assistant',
      content: `❌ New page creation cancelled.`,
      timestamp: new Date()
    });
  };



  // Handle page delete
  const handlePageDelete = async (pageId: string) => {
    try {
      // Call API to delete page
      const response = await fetch(`${API_BASE}/page_gen/session/delete`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          sessionId: pageId
        })
      });

      if (response.ok) {
        // If we're deleting the current page, clear the editor completely
        if (currentSessionId === pageId) {
          console.log('🗑️ Deleting currently active page, clearing all content');

          // Clear local session state
          setCurrentSessionId(null);

          // Clear all content in editor state
          actions.setHtmlContent('');
          actions.setStableIframeContent('');
          actions.setStreamingContent('');

          // Clear generation state
          actions.setIsGenerating(false);

          // Remove the page from state.pages if it exists
          const pageInState = state.pages.find(p => p.id === pageId);
          if (pageInState) {
            // Switch away from the deleted page (this will clear currentPageId)
            const remainingPages = state.pages.filter(p => p.id !== pageId);
            if (remainingPages.length > 0) {
              actions.switchToPage(remainingPages[0].id);
            }
          }
        }

        // Refresh the pages list
        await refreshProjectPages();

        // Check if this was the last page - if so, show create new page UI
        const updatedPages = projectPages.filter(p => p.id !== pageId);
        if (updatedPages.length === 0) {
          console.log('📄 Last page deleted, switching to create new page mode');
          setIsCreatingNewPage(true);
          setShowPlanReview(false);
          setNewPagePrompt('');
          setGeneratedPageName('');

          // Ensure we're in a clean state for new page creation
          setCurrentSessionId(null);
          actions.setHtmlContent('');
          actions.setStableIframeContent('');
          actions.setStreamingContent('');
        }

        actions.addMessage({
          role: 'assistant',
          content: `✅ Page deleted successfully`,
          timestamp: new Date()
        });
      } else {
        throw new Error('Failed to delete page');
      }
    } catch (error) {
      console.error('Error deleting page:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to delete page. Please try again.`,
        timestamp: new Date()
      });
    } finally {
      setShowDeleteConfirm(null);
    }
  };



  // Cancel delete
  const cancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  // Confirm delete
  const confirmDelete = (pageId: string) => {
    handlePageDelete(pageId);
  };



  // Generate page name when prompt changes using the new service
  useEffect(() => {
    if (newPagePrompt.trim()) {
      // Add a small delay to debounce rapid changes
      const timeoutId = setTimeout(async () => {
        try {
          const result = await createPageWithCleanName(
            { prompt: newPagePrompt },
            'new-page-flow'
          );

          if (result.success && result.pageName) {
            setGeneratedPageName(result.pageName);
          } else {
            console.warn('Page name generation failed:', result.error);
            setGeneratedPageName('New Page'); // Fallback
          }
        } catch (error) {
          console.error('❌ Error generating page name:', error);
          setGeneratedPageName('New Page'); // Fallback
        }
      }, 300); // 300ms debounce

      return () => clearTimeout(timeoutId);
    } else {
      setGeneratedPageName('');
    }
  }, [newPagePrompt]);

  // Reset isCreatingNewPage when content is available
  useEffect(() => {
    if ((state.htmlContent || state.stableIframeContent) && isCreatingNewPage && !state.isGenerating) {
      console.log('🔄 Content available, resetting isCreatingNewPage');
      setIsCreatingNewPage(false);
    }
  }, [state.htmlContent, state.stableIframeContent, isCreatingNewPage, state.isGenerating]);

  // Enhancement on submit (moved from auto-typing trigger)
  const triggerEnhancement = async (message: string) => {
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    if (!hasContent || message.length < 10) return false;

    setIsEnhancing(true);
    try {
      const htmlContent = state.htmlContent || state.stableIframeContent;
      const pageId = state.currentPageId || currentSessionId;

      // Extract element selector parameters
      const editParams = extractEditParameters(selectedElement);

      // 🚀 OPTIMIZATION: Use pageId if available, otherwise use htmlContent
      const enhancementResult = await previewEnhancement(
        message,
        htmlContent,
        pageId?.toString(),
        editParams.elementSelector,
        editParams.fragmentHtml
      );
      if (enhancementResult && enhancementResult.confidence !== 'low') {
        setEnhancement(enhancementResult);
        setShowEnhancement(true);
        setOriginalMessage(message); // Store original message since input is cleared
        return true; // Enhancement shown, wait for user choice
      }
    } catch (error) {
      console.error('Enhancement preview failed:', error);
    } finally {
      setIsEnhancing(false);
    }
    return false; // No enhancement, proceed normally
  };

  const handleLinkPages = async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);

    if (pagesWithContent.length < 2) {
      actions.addMessage({
        role: 'assistant',
        content: '⚠️ Need at least 2 pages with content to link navigation',
        timestamp: new Date()
      });
      return;
    }

    // Prevent multiple linking operations
    if (linkingProgress) {
      console.log('🔗 Linking already in progress, ignoring duplicate call');
      return;
    }

    // Close linking dialog if open
    setShowLinkingDialog(false);

    // Initialize progress tracking
    setLinkingProgress({
      current: 0,
      total: pagesWithContent.length,
      currentPage: 'Starting...'
    });

    actions.addMessage({
      role: 'assistant',
      content: `🔗 Linking ${pagesWithContent.length} pages with navigation...`,
      timestamp: new Date()
    });

    try {
      // Use the improved linking with progress callback
      await linkPagesWithProgress(pagesWithContent);

      actions.addMessage({
        role: 'assistant',
        content: '✅ All pages have been linked with navigation!',
        timestamp: new Date()
      });

      // Reset linking suggestion state so it can show again for new pages
      setHasShownLinkingSuggestion(false);
    } catch (error) {
      console.error('Linking failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: '❌ Failed to link some pages. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setLinkingProgress(null);
    }
  };

  // CLIENT-SIDE LINKING: Fast, immediate, no API calls needed
  const linkPagesWithProgress = async (pages: any[]) => {
    console.log(`🔗 Starting CLIENT-SIDE linking for ${pages.length} pages`);

    // Process all pages immediately on client-side
    pages.forEach((page, index) => {
      const otherPageNames = pages
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      console.log(`🔗 Client-side linking page ${index + 1}/${pages.length}: ${page.name}`);

      // Update progress for this page
      setLinkingProgress({
        current: index + 1,
        total: pages.length,
        currentPage: page.name
      });

      try {
        // Parse the HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(page.content, 'text/html');

        // Find navigation area (try multiple selectors)
        let navElement = doc.querySelector('nav') ||
          doc.querySelector('.nav') ||
          doc.querySelector('.navigation') ||
          doc.querySelector('header nav') ||
          doc.querySelector('.header nav');

        if (!navElement) {
          // If no nav found, try to find header and add nav there
          const header = doc.querySelector('header') || doc.querySelector('.header');
          if (header) {
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            header.appendChild(navElement);
          } else {
            // Create a simple nav at the top of body
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            navElement.style.cssText = 'padding: 1rem; background: #f8f9fa; border-bottom: 1px solid #dee2e6;';
            doc.body.insertBefore(navElement, doc.body.firstChild);
          }
        }

        // Clear existing auto-generated navigation links (but keep original nav content)
        const existingAutoLinks = navElement.querySelectorAll('a[data-page-link="true"]');
        existingAutoLinks.forEach(link => link.remove());

        // Also remove any links that match other page names (to prevent duplicates)
        const allLinks = navElement.querySelectorAll('a');
        allLinks.forEach(link => {
          const linkText = link.textContent?.trim().toLowerCase();
          const isPageLink = otherPageNames.some(pageName => {
            const normalizedPageName = pageName.toLowerCase();
            return linkText === normalizedPageName ||
              linkText === (normalizedPageName + ' page') ||
              linkText === normalizedPageName.replace(' page', '') ||
              (linkText + ' page') === normalizedPageName;
          });
          if (isPageLink) {
            link.remove();
          }
        });

        // Add links to other pages with proper navigation attributes
        otherPageNames.forEach(pageName => {
          const link = doc.createElement('a');
          link.href = '#';

          // Use the clean page name for display (remove " Page" suffix if present)
          const displayName = pageName.endsWith(' Page') ? pageName.replace(' Page', '') : pageName;
          link.textContent = displayName;

          // Use data-nav attribute for proper navigation (this is what the system expects)
          const pageId = pageName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .replace(/^-+|-+$/g, '');

          link.setAttribute('data-nav', pageId);
          link.setAttribute('data-page-link', 'true'); // For our tracking
          link.style.cssText = 'margin-right: 1rem; color: #007bff; text-decoration: none; cursor: pointer;';

          navElement.appendChild(link);
        });

        // Ensure the page has proper SPA router script for navigation
        let scriptElement = doc.querySelector('script[data-exec="inline"]');
        if (!scriptElement) {
          scriptElement = doc.createElement('script');
          scriptElement.setAttribute('data-exec', 'inline');
          scriptElement.textContent = `
// SPA Router for page navigation
document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    const pageName = target.textContent.trim();

    console.log('🔗 Navigation click in iframe:', {
      pageId,
      pageName,
      targetElement: target.outerHTML
    });

    // Send message to parent to switch pages
    if (window.parent && window.parent.postMessage) {
      window.parent.postMessage({
        type: 'NAVIGATE_TO_PAGE',
        pageId: pageId,
        pageName: pageName
      }, '*');
    }
  }
});
          `;
          doc.body.appendChild(scriptElement);
        }

        // Update the page content with modified HTML
        const updatedHtml = doc.documentElement.outerHTML;
        actions.updatePage(page.id, { content: updatedHtml });
        console.log(`✅ Client-side linking completed for ${page.name}`);

      } catch (error) {
        console.error(`❌ Failed to update page ${page.name}:`, error);
      }
    });

    // No need to wait for API calls - everything is done immediately!
    console.log('🔗 All pages linked instantly via client-side manipulation');
  };

  // ============================================================================
  // SPASHELL INTEGRATION FUNCTIONS
  // ============================================================================

  // Handle SPAShell edit mode toggle
  const handleSPAEditModeToggle = () => {
    setSpaEditMode(!spaEditMode);
    console.log(`🔄 SPAShell edit mode: ${!spaEditMode ? 'enabled' : 'disabled'}`);
  };

  // Determine display mode based on current state (extracted to avoid duplication)
  const getDisplayMode = () => {
    console.log('🎯 getDisplayMode() called with state:', {
      isGenerating: state.isGenerating,
      currentSessionId,
      hasHtmlContent: !!state.htmlContent,
      hasStableContent: !!state.stableIframeContent,
      hasStreamingContent: !!state.streamingContent,
      isCreatingNewPage,
      projectPagesLength: projectPages.length,
      isLoadingProjectPages
    });

    // Priority 1: Show new page creation UI when explicitly creating or no pages exist
    // This takes precedence over generation state to ensure "New Page" button works correctly
    if (isCreatingNewPage || (projectId && projectPages.length === 0 && !isLoadingProjectPages && !state.isGenerating)) {
      console.log('🎯 getDisplayMode() returning "creating" - creating new page or no pages (PRIORITY 1)');
      return 'creating';
    }

    // Priority 2: Show generation UI when actively generating content for existing pages
    // BUT: If we're loading an existing page (not generating), prioritize content display
    if (state.isGenerating && !isLoadingPage && !isCreatingNewPage) {
      console.log('🎯 getDisplayMode() returning "generating" - isGenerating is true and not loading page or creating');
      return 'generating';
    }

    // Special case: If we're loading a page and have isGenerating=true, it's likely a state sync issue
    // In this case, prioritize showing content if we have it
    if (state.isGenerating && isLoadingPage) {
      console.log('🎯 getDisplayMode() detected state sync issue - isGenerating=true but loading existing page');
      const hasContent = state.htmlContent || state.stableIframeContent;
      const currentPage = projectPages.find(p => p.id === currentSessionId);

      if (currentSessionId && (hasContent || currentPage)) {
        console.log('🎯 getDisplayMode() returning "content" - overriding isGenerating due to page loading');
        return 'content';
      }
    }

    // Priority 3: Show page content when we have a selected page
    // Check editor state content first, or if we have a valid selected page
    const hasContent = state.htmlContent || state.stableIframeContent;
    const currentPage = projectPages.find(p => p.id === currentSessionId);

    if (currentSessionId && (hasContent || currentPage)) {
      console.log('🎯 getDisplayMode() returning "content" - has page and content');
      return 'content';
    }

    // Priority 4: Show empty state
    console.log('🎯 getDisplayMode() returning "empty" - default case');
    return 'empty';
  };





  const handleImplementChoice = async (choice: 'inline' | 'modal' | 'page') => {
    console.log('🔥 handleImplementChoice called with:', choice);
    console.log('🔥 selectedElement:', state.selectedElement);
    console.log('🔥 isGenerating:', state.isGenerating);

    if (!state.selectedElement) {
      console.log('🔥 No selected element, returning');
      return;
    }

    // Prevent multiple implementation calls
    if (state.isGenerating) {
      console.log('🔥 Implementation already in progress, ignoring');
      return;
    }

    console.log('🔥 Closing implementation modal');

    // CRITICAL FIX: Capture selected element data BEFORE clearing state
    const selectedElementData = state.selectedElement;
    const elementText = selectedElementData?.textContent || 'element';
    const elementType = selectedElementData?.implementationType || 'interactive';

    console.log('🔍 Captured element data before state clearing:', {
      hasElement: !!selectedElementData,
      elementText,
      elementType,
      hasOuterHTML: !!selectedElementData?.outerHTML
    });

    actions.setShowImplementModal(false);

    // Auto-disable edit mode after implementation choice
    if (spaEditMode) {
      console.log('🔥 Auto-disabling edit mode after implementation');
      setSpaEditMode(false);
    }

    // Clear custom functionality after implementation
    actions.setCustomFunctionality('');

    // Use custom functionality if provided, otherwise use default description
    const functionalityDescription = state.customFunctionality.trim() ||
      `Implement "${elementText}" functionality`;

    // Add user message showing their choice
    actions.addMessage({
      role: 'user',
      content: `${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : choice === 'modal' ? 'a modal/popup' : 'a new page'}`,
      timestamp: new Date()
    });

    if (choice === 'page') {
      // Handle page creation aligned with main page creation workflow
      try {
        const result = await createPageWithCleanName(
          { prompt: elementText },
          'element-implementation'
        );

        if (!result.success) {
          throw new Error(result.error || 'Failed to create page');
        }

        const { pageName, pageId } = result;

        // Check if page already exists
        const existingPage = state.pages.find(p => p.id === pageId);
        if (existingPage) {
          actions.switchToPage(pageId!);
          actions.addMessage({
            role: 'assistant',
            content: `✅ Switched to existing "${pageName}" page`,
            timestamp: new Date()
          });
          return;
        }

        // Clear current page selection to prepare for new page creation
        setCurrentSessionId(null);
        actions.setHtmlContent('');
        actions.setStableIframeContent('');

        // Add assistant message about page creation
        actions.addMessage({
          role: 'assistant',
          content: `🚀 Creating "${pageName}" page for this feature...`,
          timestamp: new Date()
        });

        // Generate content using the same pattern as main page creation
        // This will auto-save the page after HTML generation completes
        const prompt = generatePageContentPrompt(pageName!, state.pages);
        await actions.generateFromPrompt(prompt, pageName!);

        // Page will be automatically refreshed and switched when auto-save completes
        // via the pageSaved event listener

        console.log('🔗 Element-based page created successfully. Use Link Pages button to add navigation.');

      } catch (error) {
        console.error('❌ Error creating page:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to create page. Please try again.`,
          timestamp: new Date()
        });
      }
    } else {
      // Handle inline or modal implementation using editContent (includes conversation history)
      try {
        // Create intent and user messages to include in conversation history
        const additionalMessages: ChatMessage[] = [];

        // Add intent message if available
        if (selectedElementData?.intentData) {
          additionalMessages.push({
            role: 'assistant',
            content: `🎯 **Intent Analysis:**\n${selectedElementData.intentData.userIntent}\n\n💡 **Suggestion:**\n${selectedElementData.intentData.suggestion || "I can help implement this functionality."}`,
            timestamp: new Date()
          });
        }

        // Add user message
        const userMessage: ChatMessage = {
          role: 'user',
          content: `${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}`,
          timestamp: new Date()
        };
        additionalMessages.push(userMessage);

        // Add messages to chat UI
        additionalMessages.forEach(msg => actions.addMessage(msg));

        // Create implementation prompt that references the intent analysis
        const intentContext = selectedElementData?.intentData
          ? `Based on the intent analysis above: "${selectedElementData.intentData.userIntent}"

${selectedElementData.intentData.suggestion || "Please implement this functionality."}

` : '';

        // Use custom functionality description if provided
        const customDescription = state.customFunctionality.trim()
          ? `\n\nUser's specific requirements: "${state.customFunctionality}"\n`
          : '';

        // Determine if this is a rename/replace operation
        const isRenameOperation = state.customFunctionality.toLowerCase().includes('rename') ||
          state.customFunctionality.toLowerCase().includes('change to') ||
          state.customFunctionality.toLowerCase().includes('replace with');

        const implementationPrompt = `${intentContext}${functionalityDescription} as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}.${customDescription}

Context: User clicked on an element and wants to implement functionality. Please modify the existing content ${isRenameOperation ? 'by REPLACING/RENAMING the clicked element' : 'to add this feature'} while preserving the current design and layout.

Implementation type: ${choice}
Element type: ${elementType}
Selected element: "${elementText}"

${choice === 'modal' ? 'Create a modal/popup that opens when the element is clicked.' : isRenameOperation ? 'REPLACE the selected element with the new functionality - do not add new elements.' : 'Add the functionality directly to the current page.'}

Important: ${isRenameOperation ? 'This is a REPLACEMENT operation - modify the existing "' + elementText + '" element, do not create new elements.' : state.customFunctionality.trim() ? 'Follow the user\'s specific requirements above exactly.' : 'The intent analysis and suggestion above should guide your implementation approach.'}`;

        // Use editContent with explicit conversation history including intent
        // Pass element selector, implementation type, and captured element data for better targeting
        await actions.editContent(implementationPrompt, additionalMessages, selectedElementData?.tagName?.toLowerCase(), choice, selectedElementData);

        // After successful edit, reload the page content from database to ensure consistency
        if (currentSessionId) {
          console.log('🔄 Reloading page content after successful implementation...');
          const reloadSuccess = await actions.reloadCurrentPage(currentSessionId);
          if (reloadSuccess) {
            console.log('✅ Page content reloaded successfully after implementation');
          } else {
            console.warn('⚠️ Failed to reload page content after implementation');
          }
        }

        actions.addMessage({
          role: 'assistant',
          content: `✅ Successfully implemented "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}!`,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Implementation error:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to implement "${elementText}". Please try again.`,
          timestamp: new Date()
        });
      }
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatPlanForDisplay = (plan: any): string => {
    if (typeof plan === 'string') {
      return plan;
    }

    if (plan && typeof plan === 'object') {
      let planContent = '';

      // Add overview
      if (plan.overview) {
        planContent += `${plan.overview}\n\n`;
      }

      // Add sections
      if (plan.sections && Array.isArray(plan.sections)) {
        plan.sections.forEach((section: any, index: number) => {
          planContent += `${index + 1}. ${section.title}\n`;
          if (section.description) {
            planContent += `${section.description}\n`;
          }
          if (section.details && Array.isArray(section.details)) {
            section.details.forEach((detail: string) => {
              planContent += `• ${detail}\n`;
            });
          }
          planContent += '\n';
        });
      }

      // Add features
      if (plan.features && Array.isArray(plan.features)) {
        planContent += `Key Features:\n`;
        plan.features.forEach((feature: string) => {
          planContent += `• ${feature}\n`;
        });
        planContent += '\n';
      }

      // Add accessibility
      if (plan.accessibility && Array.isArray(plan.accessibility)) {
        planContent += `Accessibility:\n`;
        plan.accessibility.forEach((item: string) => {
          planContent += `• ${item}\n`;
        });
      }

      return planContent.trim();
    }

    return '';
  };



  // All page creation utility functions are now handled by the new services

  // ============================================================================
  // RENDER
  // ============================================================================

  // Show project selector if no project is specified
  if (!projectId) {
    return (
      <div className="h-screen bg-gray-50 flex items-center justify-center">
        <div className="max-w-4xl w-full mx-4">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Editor</h1>
              <p className="text-gray-600">Select a project to open in the editor</p>
            </div>

            {isLoadingProjects ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-3"></div>
                <span className="text-gray-600">Loading projects...</span>
              </div>
            ) : availableProjects.length === 0 ? (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                <p className="text-gray-600 mb-6">Create your first project to get started</p>
                <button
                  onClick={() => navigate('/prototypes')}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Go to My Prototypes
                </button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableProjects.map((project) => (
                  <div
                    key={project.id}
                    onClick={() => handleProjectSelect(project.id)}
                    className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors"
                  >
                    <h3 className="font-medium text-gray-900 mb-2">{project.title}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {project.description || 'No description'}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>Updated {new Date(project.updated_at).toLocaleDateString()}</span>
                      <span className="px-2 py-1 bg-gray-100 rounded text-gray-700">{project.status}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="mt-8 text-center">
              <button
                onClick={() => navigate('/prototypes')}
                className="text-blue-600 hover:text-blue-700 transition-colors"
              >
                ← Back to My Prototypes
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }  return (
    <ErrorBoundary>
      {/* Main Container - Full Viewport Height */}
      <div className="h-screen flex flex-col overflow-hidden">
      {/* Enhanced Progress Indicator - Below header, always visible when generating */}
      {state.isGenerating && (
        <div className="fixed top-20 left-0 right-0 bg-gradient-to-r from-blue-600 via-blue-500 to-blue-600 text-white shadow-xl z-[9999] animate-pulse">
          {/* Animated background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>

          {/* Moving progress bar */}
          <div className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-300 to-white animate-pulse">
            <div className="h-full bg-white/50 animate-pulse" style={{
              width: '100%',
              animation: 'progressSlide 2s ease-in-out infinite'
            }}></div>
          </div>

          <div className="relative px-6 py-4">
            <div className="flex items-center justify-center space-x-4">
              {/* Enhanced bouncing dots */}
              <div className="flex space-x-1.5">
                <div className="w-3 h-3 bg-white rounded-full shadow-lg" style={{
                  animation: 'enhancedBounce 1.4s ease-in-out infinite'
                }}></div>
                <div className="w-3 h-3 bg-white rounded-full shadow-lg" style={{
                  animation: 'enhancedBounce 1.4s ease-in-out infinite',
                  animationDelay: '0.2s'
                }}></div>
                <div className="w-3 h-3 bg-white rounded-full shadow-lg" style={{
                  animation: 'enhancedBounce 1.4s ease-in-out infinite',
                  animationDelay: '0.4s'
                }}></div>
              </div>

              {/* Animated text with glow effect */}
              <span className="text-white font-semibold text-lg drop-shadow-lg animate-pulse">
                🤖 AI is generating your prototype...
              </span>

              {/* Spinning icon */}
              <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          </div>

          {/* CSS animations */}
          <style jsx>{`
            @keyframes enhancedBounce {
              0%, 80%, 100% {
                transform: scale(0.8) translateY(0);
                opacity: 0.7;
              }
              40% {
                transform: scale(1.2) translateY(-12px);
                opacity: 1;
              }
            }

            @keyframes progressSlide {
              0% {
                transform: translateX(-100%);
                opacity: 0;
              }
              50% {
                opacity: 1;
              }
              100% {
                transform: translateX(100%);
                opacity: 0;
              }
            }
          `}</style>
        </div>
      )}

      {/* Header/Navigation Bar */}
      <div className={styles.headerBar}>
        {/* Left Section - JustPrototype Branding and Toggle */}
        <div className={styles.headerLeft}>
          {/* JustPrototype Branding - FIRST in header */}
          <Link to="/" className="flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Sparkles className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                JustPrototype
              </h1>
            </div>
          </Link>

          {/* Left Pane Toggle Button - SECOND in header */}
          <button
            onClick={() => setIsLeftPaneCollapsed(!isLeftPaneCollapsed)}
            className={styles.toggleButton}
          >
            <Bars3Icon className={`w-5 h-5 ${isLeftPaneCollapsed ? '' : 'rotate-180'}`} />
          </button>
        </div>

        {/* Mobile Tab Navigation - THIRD (mobile only) */}
        {isMobile && (
          <div className={styles.mobileTabNav}>
            <button
              onClick={() => setMobileActiveTab('preview')}
              className={`${styles.mobileTab} ${
                mobileActiveTab === 'preview' ? styles.mobileTabActive : styles.mobileTabInactive
              }`}
            >
              Preview
            </button>
            <button
              onClick={() => setMobileActiveTab('chat')}
              className={`${styles.mobileTab} ${
                mobileActiveTab === 'chat' ? styles.mobileTabActive : styles.mobileTabInactive
              }`}
            >
              Chat
            </button>
          </div>
        )}

        {/* Right Section - Share Button LAST */}
        <div className="flex items-center space-x-2">
          {/* Share Button */}
          {state.htmlContent && (
            <ShareButton
              prototypeId={urlProjectId?.toString() || currentSessionId || 'current-page'}
              prototypeName={
                projectPages.find(p => p.id.toString() === (urlProjectId?.toString() || currentSessionId))?.title ||
                state.pages.find(p => p.id === (urlProjectId?.toString() || currentSessionId))?.name ||
                'My Prototype'
              }
              variant="text"
              size="small"
              className="!p-2 !rounded-full hover:bg-gray-100/70 text-gray-500 hover:text-gray-700 !border-0 !bg-transparent"
            />
          )}
        </div>
      </div>

      {/* Modern 3-Column AI Chat Layout */}
      <div className={`flex-1 flex flex-col ${state.isGenerating ? 'pt-16' : ''}`}>
        <div className="h-full flex flex-row relative">
          {/* Mobile Layout */}
          {isMobile ? (
            <div className={styles.mobileContentArea}>
              {/* Mobile Preview Tab */}
              {mobileActiveTab === 'preview' && (
                <div className={`${styles.mobilePreviewTab} ${styles.fadeIn}`}>
                  {/* Main Content Area */}
                  <div className={`flex-1 flex ${state.isGenerating ? 'p-0' : 'items-center justify-center p-4'} overflow-hidden`}>
                    <div className={`w-full ${state.isGenerating ? 'h-full' : 'bg-white rounded-2xl shadow-xl border border-gray-200 max-h-[calc(100vh-200px)]'} flex flex-col`}>
                      {/* Content based on current state - Same logic as desktop */}
                      {(() => {
                        // Debug logging for content display logic
                        console.log('🎯 Mobile Content display logic check:', {
                          isCreatingNewPage,
                          hasProjectPages: projectPages.length > 0,
                          currentSessionId,
                          isGenerating: state.isGenerating,
                          hasHtmlContent: !!state.htmlContent,
                          hasStableContent: !!state.stableIframeContent,
                          isLoadingPage,
                          isLoadingProjectPages
                        });

                        // Show plan review if we have a plan and are not generating
                        if (state.plan && !state.isGenerating) {
                          return (
                            <div className="flex-1 flex items-center justify-center p-6">
                              <div className="w-full max-w-2xl">
                                <PlanDisplay
                                  plan={state.plan}
                                  onApprove={handleGeneratePageFromPlan}
                                  onReject={handleRejectPlan}
                                  isGenerating={state.isGenerating}
                                />
                              </div>
                            </div>
                          );
                        }

                        // Show new page creation interface
                        if (isCreatingNewPage) {
                          return (
                            <div className="flex-1 flex items-center justify-center p-6">
                              <div className="w-full max-w-2xl text-center">
                                <div className="mb-6">
                                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Create New Page</h2>
                                  <p className="text-gray-600">Describe what you want to build</p>
                                </div>

                                <PromptInput
                                  value={newPagePrompt}
                                  onChange={setNewPagePrompt}
                                  onSubmit={handleNewPagePromptSubmit}
                                  placeholder="Describe your page idea..."
                                  submitButtonText="Create Page"
                                  loadingText="Creating plan..."
                                  height="min-h-[120px]"
                                  theme="violet"
                                  isLoading={isGeneratingPlan}
                                  variant="creation"
                                />

                                <div className="flex space-x-3 mt-4">
                                  {projectPages.length > 0 && (
                                    <button
                                      onClick={handleCancelNewPage}
                                      className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                                    >
                                      Cancel
                                    </button>
                                  )}
                                </div>
                              </div>
                            </div>
                          );
                        }

                        // Show content if available
                        const cleanContent = state.htmlContent || state.stableIframeContent || '';
                        if (cleanContent.length > 0) {
                          // SAFEGUARD: Only show progressive rendering for actual generation, not page loading
                          if (cleanContent.length > 0 && state.isGenerating && !isLoadingPage) {
                            return (
                              <div className="h-full flex flex-col">
                                {/* Progressive Content Rendering - Full height */}
                                <div className="flex-1 bg-white">
                                  <SeamlessProgressiveRenderer
                                    htmlContent={cleanContent}
                                    isGenerating={state.isGenerating}
                                    onComplete={() => {
                                      console.log('🎯 Progressive rendering completed');
                                    }}
                                    className="h-full"
                                  />
                                </div>
                              </div>
                            );
                          } else {
                            // Show stable content using SPAShell
                            return (
                              <div className="h-full flex flex-col">
                                <div className="flex-1 bg-white">
                                  <SPAShell
                                    dashboardHtml={cleanContent}
                                    streamingContent=""
                                    isGenerating={false}
                                    enableEditMode={spaEditMode}
                                    viewMode={viewMode}
                                    onEditModeToggle={handleSPAEditModeToggle}
                                    onElementClick={handleSPAElementClick}
                                    className="h-full"
                                  />
                                </div>
                              </div>
                            );
                          }
                        }

                        // Show empty state
                        return (
                          <div className="flex-1 flex items-center justify-center p-6">
                            <div className="text-center">
                              <div className="w-16 h-16 mx-auto mb-4 text-gray-300">
                                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Page Selected</h3>
                              <p className="text-gray-600">Select a page from the sidebar or create a new one</p>
                            </div>
                          </div>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              )}

              {/* Mobile Chat Tab */}
              {mobileActiveTab === 'chat' && (
                <div className={`${styles.mobileChatTab} ${styles.fadeIn}`}>
                  {/* Chat Messages */}
                  <div className={styles.mobileChatMessages}>
                    {state.messages.map((message, index) => (
                      <div key={index}>
                        {message.role === 'user' ? (
                          /* User Message */
                          <div className="flex justify-end">
                            <div className="max-w-[80%] bg-violet-600 text-white rounded-2xl rounded-br-md px-4 py-3">
                              <div className="text-sm leading-relaxed whitespace-pre-wrap">
                                {message.content}
                              </div>
                              <div className="text-xs text-violet-100 mt-1 opacity-75">
                                {message.timestamp.toLocaleTimeString([], {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            </div>
                          </div>
                        ) : (
                          /* Assistant Message */
                          <div className="flex justify-start">
                            <div className="max-w-[80%] bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                              <div className="text-sm leading-relaxed text-gray-700">
                                {message.content}
                              </div>
                              <div className="text-xs text-gray-500 mt-1">
                                {message.timestamp.toLocaleTimeString([], {
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}

                    {/* Typing Indicator */}
                    {state.isGenerating && (
                      <div className="flex justify-start">
                        <div className="bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                          <div className="flex items-center space-x-1">
                            <div className="flex space-x-1">
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                              <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                            </div>
                            <span className="text-xs text-gray-500 ml-2">AI is thinking...</span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Element Selector Controls - Mobile */}
                  <div className="flex-shrink-0 p-4 border-t border-gray-100">
                    <EditModeControls
                      isEditModeActive={isEditModeActive}
                      selectedElement={selectedElement}
                      onToggleEditMode={() => elementSelectorManager.toggleEditMode()}
                      onClearSelection={() => elementSelectorManager.clearSelection()}
                      onExitEditMode={() => elementSelectorManager.exitEditMode()}
                    />
                  </div>

                  {/* Chat Input - Mobile */}
                  <div className={styles.mobileChatInput}>
                    <PromptInput
                      value={state.input}
                      onChange={actions.setInput}
                      onSubmit={(value) => {
                        if (value.trim() && !state.isGenerating) {
                          handleChatSubmit(value.trim());
                        }
                      }}
                      placeholder={state.isGenerating ? "AI is working..." : "Tell me what to change, specific and clear. One task at a time."}
                      submitButtonText=""
                      loadingText=""
                      height="min-h-[100px] max-h-[200px]"
                      theme="violet"
                      isLoading={state.isGenerating}
                      variant="chat"
                      showSubmitButton={true}
                      additionalContent={
                        <div className="flex justify-start mt-3">
                          <button
                            type="button"
                            onClick={() => setElementSelectorActive(!elementSelectorActive)}
                            className={`flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium transition-colors ${elementSelectorActive
                              ? 'bg-violet-600 text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                              }`}
                            style={{ minHeight: '44px' }}
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                            </svg>
                            <span>Selector</span>
                          </button>
                        </div>
                      }
                    />
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Desktop Layout - Three-pane layout */
            <div className={styles.desktopLayout}>
              {/* Left Panel (Sidebar) - Using shared PageSidebar component */}
              <PageSidebar
                isOpen={!isLeftPaneCollapsed}
                onClose={() => setIsLeftPaneCollapsed(true)}
                project={projectId ? { id: projectId, title: '', status: 'active', type: 'prototype', created_at: '', updated_at: '' } : null}
                pages={projectPages}
                currentPageId={currentSessionId}
                isLoadingPages={isLoadingProjectPages}
                isLoadingPage={isLoadingPage}
                loadingPageId={loadingPageId}
                onPageSelect={handleProjectPageSelect}
                onCreatePage={handleCreateNewPage}
                onPageRename={handlePageRenameShared}
                onPageDelete={handlePageDeleteShared}
                onLinkPages={handleLinkPages}
                theme="violet"
                variant="inline"
                showNewPageButton={true}
                showPageActions={true}
              />

              {/* Center Panel (Main Canvas) - Full height during generation */}
              <div className={`${styles.desktopCenterPanel} ${isLeftPaneCollapsed ? 'w-full' : ''}`}>
            {/* Main Content Area */}
            <div className={`flex-1 flex ${state.isGenerating ? 'p-0' : 'items-center justify-center p-6'} overflow-hidden`}>
              <div className={`w-full ${isLeftPaneCollapsed ? 'w-full' : state.isGenerating ? 'w-full' : 'max-w-4xl'} ${state.isGenerating ? 'h-full' : 'bg-white rounded-2xl shadow-xl border border-gray-200 max-h-[calc(100vh-160px)]'} flex flex-col`}>
                {/* Content based on current state */}
                {(() => {
                  // Debug logging for content display logic
                  console.log('🎯 Content display logic check:', {
                    isCreatingNewPage,
                    hasProjectPages: projectPages.length > 0,
                    isLoadingProjectPages,
                    isGenerating: state.isGenerating,
                    hasHtmlContent: !!(state.htmlContent || state.stableIframeContent),
                    currentSessionId,
                    stateCurrentPageId: state.currentPageId,
                    htmlContentLength: state.htmlContent?.length || 0,
                    stableContentLength: state.stableIframeContent?.length || 0
                  });

                  return getDisplayMode();
                })() === 'creating' ? (
                  /* New Page Creation UI */
                  <div className="p-8 overflow-y-auto">
                    {isGeneratingPlan ? (
                      /* Plan Generation Loading */
                      <div className="space-y-6 text-center">
                        <div className="text-center">
                          <h2 className="text-xl font-semibold text-gray-900 mb-2">Generating Plan</h2>
                          <p className="text-gray-600">Page: {generatedPageName}</p>
                        </div>

                        <div className="flex flex-col items-center space-y-4">
                          <div className="w-16 h-16 border-4 border-violet-600 border-t-transparent rounded-full animate-spin"></div>
                          <div className="text-center">
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating Your Plan</h3>
                            <p className="text-gray-600">Analyzing your requirements and crafting a detailed implementation strategy...</p>
                          </div>
                        </div>
                      </div>
                    ) : showPlanReview ? (
                      /* Plan Review - Structured like PlanReviewPageV3 */
                      <div className="h-full overflow-y-auto">
                        {/* Header */}
                        <div className="bg-white border-b border-gray-200 p-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <h1 className="text-2xl font-bold text-gray-900">Design Plan</h1>
                              <p className="text-gray-600 text-sm mt-1">
                                Review the detailed implementation plan
                                {generatedPageName && (
                                  <span className="ml-2">• Page: <span className="font-medium text-blue-600">{generatedPageName}</span></span>
                                )}
                              </p>
                            </div>
                            <div className="flex items-center space-x-3">
                              <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-700">
                                🖥️ Desktop
                              </span>
                              <button
                                onClick={handleGeneratePageFromPlan}
                                disabled={state.isGenerating}
                                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                              >
                                {state.isGenerating ? (
                                  <>
                                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                    Generating...
                                  </>
                                ) : (
                                  <>
                                    Generate
                                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                                    </svg>
                                  </>
                                )}
                              </button>
                            </div>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="p-6">
                          <PlanDisplay planData={generatedPlan} />
                        </div>

                        {/* Bottom Actions */}
                        <div className="bg-white border-t border-gray-200 p-6">
                          <div className="flex space-x-3">
                            <button
                              onClick={handleRejectPlan}
                              className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                            >
                              Edit Prompt
                            </button>
                            <button
                              onClick={handleGeneratePageFromPlan}
                              disabled={state.isGenerating}
                              className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                            >
                              {state.isGenerating ? (
                                <>
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                  Generating...
                                </>
                              ) : (
                                'Generate Page'
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    ) : (
                      /* Prompt Input */
                      <div className="space-y-6">
                        <div className="text-center">
                          <h2 className="text-xl font-semibold text-gray-900 mb-2">Create New Page</h2>
                          <p className="text-gray-600">Describe what kind of page you'd like to create</p>
                        </div>

                        <PromptInput
                          value={newPagePrompt}
                          onChange={setNewPagePrompt}
                          onSubmit={(value) => {
                            setNewPagePrompt(value);
                            handleNewPagePromptSubmit();
                          }}
                          placeholder="Describe your page... (e.g., 'Create a modern login form with email and password fields')"
                          submitButtonText="Create Plan"
                          loadingText="Generating Plan..."
                          height="h-32"
                          theme="violet"
                          isLoading={isGeneratingPlan}
                          variant="inline"
                          showSubmitButton={false}
                          additionalContent={
                            <div className="space-y-4">
                              {generatedPageName && (
                                <div className="text-sm text-gray-600">
                                  Page name: <span className="font-medium">{generatedPageName}</span>
                                </div>
                              )}

                              <div className="flex space-x-3">
                                {projectPages.length > 0 && (
                                  <button
                                    onClick={handleCancelNewPage}
                                    className="flex-1 px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-colors font-medium"
                                  >
                                    Cancel
                                  </button>
                                )}
                                <button
                                  onClick={handleNewPagePromptSubmit}
                                  disabled={!newPagePrompt.trim() || isGeneratingPlan}
                                  className="flex-1 px-4 py-3 bg-violet-600 text-white rounded-xl hover:bg-violet-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center justify-center"
                                >
                                  {isGeneratingPlan ? (
                                    <>
                                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                      Generating Plan...
                                    </>
                                  ) : (
                                    'Create Plan'
                                  )}
                                </button>
                              </div>
                            </div>
                          }
                        />
                      </div>
                    )}
                  </div>
                ) : getDisplayMode() === 'generating' ? (
                  /* Progressive Generation Rendering - Full height, no borders */
                  <div className="h-full relative bg-gray-50">
                    {/* Progressive Content Container */}
                    <div className="h-full overflow-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:bg-transparent [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300/60 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400/60">
                      <div className="p-6" style={{height: '100%'}}>
                        {(() => {
                          // Clean the streaming content
                          const rawContent = state.streamingContent || '';
                          const cleanContent = rawContent
                            .replace(/```html\s*/g, '')
                            .replace(/```\s*/g, '')
                            .trim();

                          console.log('🎬 Progressive rendering state:', {
                            isGenerating: state.isGenerating,
                            isLoadingPage,
                            hasStreamingContent: !!state.streamingContent,
                            streamingContentLength: state.streamingContent?.length || 0,
                            cleanContentLength: cleanContent.length,
                            cleanContentPreview: cleanContent.substring(0, 200) + '...',
                            willShowProgressiveContent: cleanContent.length > 0,
                            rawContentPreview: rawContent.substring(0, 100) + '...',
                            displayMode: 'generating'
                          });

                          // SAFEGUARD: Only show progressive rendering for actual generation, not page loading
                          if (cleanContent.length > 0 && state.isGenerating && !isLoadingPage) {
                            return (
                              <div className="h-full flex flex-col">
                                {/* Progressive Content Rendering - Full height */}
                                <div className="flex-1 bg-white">
                                  <SeamlessProgressiveRenderer
                                    htmlContent=""
                                    streamingContent={cleanContent}
                                    isGenerating={true}
                                    onElementClick={handleElementClick}
                                    className="w-full h-full"
                                  />
                                </div>


                              </div>
                            );
                          } else {
                            // SAFEGUARD: If we're in generating mode but shouldn't show progressive rendering
                            // (e.g., during page loading), show a simple loading message
                            console.log('🎯 In generating mode but not showing progressive rendering - likely page loading');
                            return (
                              <div className="h-full flex items-center justify-center">
                                <div className="text-center">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                                  <p className="text-gray-600">
                                    {generationProgress?.message || 'Loading page content...'}
                                  </p>
                                  {generationProgress && (
                                    <p className="text-sm text-gray-500 mt-2">
                                      {Math.floor((Date.now() - generationProgress.startTime) / 1000)}s elapsed
                                    </p>
                                  )}
                                </div>
                              </div>
                            );
                          }
                        })()}
                      </div>
                    </div>

                    
                  </div>          
                        ) : getDisplayMode() === 'content' ? (
                  <div className="h-full overflow-auto [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar]:bg-transparent [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-thumb]:bg-gray-300/60 hover:[&::-webkit-scrollbar-thumb]:bg-gray-400/60">
                    {(() => {
                      // For existing content, prioritize state content first, then fall back to project page content
                      let rawContent = state.htmlContent || state.stableIframeContent || '';

                      // If no content in state, we need to load it from the database
                      if (!rawContent && currentSessionId) {
                        const currentPage = projectPages.find(p => p.id === currentSessionId);
                        if (currentPage) {
                          // Page exists but content not loaded - trigger loading
                          console.log('🔄 Content not loaded for current page, triggering load...');
                          handleProjectPageSelect(currentPage);
                          return (
                            <div className="h-full flex items-center justify-center bg-gray-50">
                              <div className="text-center max-w-md mx-auto p-8">
                                <div className="mb-6">
                                  <svg className="w-16 h-16 mx-auto text-gray-300 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                  </svg>
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Page Content</h3>
                                <p className="text-gray-600">
                                  Loading content for "{currentPage.title || `Page ${currentPage.id}`}"...
                                </p>
                              </div>
                            </div>
                          );
                        }
                      }

                      const cleanContent = rawContent
                        .replace(/```html\s*/g, '')
                        .replace(/```\s*/g, '')
                        .trim();

                      console.log('🎯 SPAShell content debug:', {
                        currentSessionId,
                        hasRawContent: !!rawContent,
                        rawContentLength: rawContent.length,
                        cleanContentLength: cleanContent.length,
                        cleanContentPreview: cleanContent.substring(0, 200) + '...',
                        stateHtmlContent: !!state.htmlContent,
                        stateStableContent: !!state.stableIframeContent,
                        streamingContent: !!state.streamingContent,
                        isGenerating: state.isGenerating
                      });

                      if (!cleanContent) {
                        return (
                          <div className="h-full flex items-center justify-center bg-gray-50">
                            <div className="text-center max-w-md mx-auto p-8">
                              <div className="mb-6">
                                <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                              </div>
                              <h3 className="text-lg font-semibold text-gray-900 mb-2">Page Content Loading</h3>
                              <p className="text-gray-600">
                                The page content is being loaded. If this persists, try refreshing the page.
                              </p>
                            </div>
                          </div>
                        );
                      }

                      // CRITICAL: For existing content (not generating), render directly in SPAShell without progressive rendering
                      // This ensures instant display for page switching, never progressive rendering
                      console.log('🎯 Rendering existing content with SPAShell (NO progressive rendering):', {
                        contentLength: cleanContent.length,
                        isGenerating: state.isGenerating,
                        hasStreamingContent: !!state.streamingContent,
                        displayMode: 'content'
                      });

                      return (
                        <SPAShell
                          ref={spaShellRef}
                          className="h-full p-4"
                          enableEditMode={spaEditMode}
                          dashboardHtml={cleanContent}
                          streamingContent="" // NEVER streaming for existing content
                          isGenerating={false} // NEVER generating when showing existing content
                          onElementClick={useSPAMode ? handleElementClick : undefined}
                          viewMode={viewMode}
                          useSPAMode={useSPAMode}
                        />
                      );
                    })()}
                  </div>
                ) : (
                  /* Empty State */
                  <div className="p-8 text-center">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">No Page Selected</h3>
                    <p className="text-gray-600">Select a page from the sidebar or create a new one</p>
                  </div>
                )}
              </div>

              {/* VERSIONING DISABLED: Commented out Prototype Version Switcher */}
              {/*
              //{(() => {
                //console.log('🔍 Version switcher visibility check:', {
                  //projectId,
                  //versionLabelsLength: versionLabels.length,
                  //versionLabels,
                  //hasFirstVersion,
                  //shouldShow: projectId && versionLabels.length > 0
                //});
                //return projectId && versionLabels.length > 0;
              //})() && (
                // <div className="absolute bottom-6 left-6 z-10">
                  // <PrototypeVersionSwitcher
                    // versions={versionLabels}
                    // currentVersion={currentVersionLabel}
                    // onVersionChange={(versionLabel) => {
                    //   console.log('🔄 Version switcher clicked:', versionLabel);
                    //   setIsVersionSwitching(true);
                  //     switchToVersion(versionLabel);
                  //   }}
                  //   isLoading={isVersionLoading || isVersionCreationInProgress}
                  // />

                  // {isVersionCreationInProgress && (
                  //   <div className="mt-2 px-3 py-1 bg-blue-50 border border-blue-200 rounded-lg backdrop-blur-sm">
                  //     <div className="flex items-center space-x-2">
                  //       <div className="animate-spin h-3 w-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                  //       <span className="text-blue-700 text-xs font-medium">Creating version...</span>
                  //     </div>
                  //   </div>
                  // )}

              //     {isVersionSwitching && (
              //       <div className="mt-2 px-3 py-1 bg-green-50 border border-green-200 rounded-lg backdrop-blur-sm">
              //         <div className="flex items-center space-x-2">
              //           <div className="animate-spin h-3 w-3 border-2 border-green-500 border-t-transparent rounded-full"></div>
              //           <span className="text-green-700 text-xs font-medium">Switching version...</span>
              //         </div>
              //       </div>
              //     )}
              //   </div>
              // )}

              // {versionError && (
              //   <div className="absolute bottom-20 left-6 z-10 max-w-sm">
              //     <div className="bg-red-50 border border-red-200 rounded-lg p-3 shadow-lg backdrop-blur-sm">
              //       <div className="flex items-center justify-between">
              //         <div className="text-red-600 text-sm">{versionError}</div>
              //         <button
              //           onClick={clearVersionError}
              //           className="ml-2 text-red-500 hover:text-red-700"
              //           title="Dismiss error"
              //         >
              //           ×
              //         </button>
              //       </div>
              //     </div>
              //   </div>
              // )}
              */}
            </div>

            {/* Floating Bottom Toolbar - Subtle Modern Design */}
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50">
              <div className="flex items-center backdrop-blur-sm bg-white/80 rounded-full shadow-sm px-1.5 py-1 border border-gray-100 transition-all duration-200 hover:shadow-md">
                {/* Code/Preview Toggle Button */}
                <button
                  onClick={() => setViewMode(viewMode === 'code' ? 'preview' : 'code')}
                  className={`flex items-center p-2 rounded-full transition-all duration-200 ${viewMode === 'code'
                    ? 'bg-violet-500/10 text-violet-600'
                    : 'text-gray-500 hover:bg-gray-100/70'
                  }`}
                  title={viewMode === 'code' ? 'Switch to Preview' : 'Switch to Code'}
                >
                  {viewMode === 'code' ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                  )}
                </button>

                {/* Divider */}
                <div className="w-px h-6 bg-gray-200 mx-1"></div>

                {/* Edit Mode Button - Only show in preview mode */}
                {viewMode === 'preview' && (
                  <button
                    onClick={handleSPAEditModeToggle}
                    className={`flex items-center p-2 rounded-full transition-all duration-200 ${
                      spaEditMode
                        ? 'bg-red-500/10 text-red-600'
                        : 'text-gray-500 hover:bg-gray-100/70'
                    }`}
                    title={spaEditMode ? 'Exit Edit Mode' : 'Enter Edit Mode'}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                )}

                {/* Share Button - Only show when we have content */}
                {(state.htmlContent || state.stableIframeContent) && (
                  <>
                    {/* Divider */}
                    <div className="w-px h-6 bg-gray-200 mx-1"></div>

                    <div className="p-1">
                      <ShareButton
                        prototypeId={urlProjectId?.toString() || currentSessionId || 'current-page'}
                        prototypeName={
                          projectPages.find(p => p.id.toString() === (urlProjectId?.toString() || currentSessionId))?.title ||
                          state.pages.find(p => p.id === (urlProjectId?.toString() || currentSessionId))?.name ||
                          'My Prototype'
                        }
                        variant="text"
                        size="small"
                        className="!p-2 !rounded-full hover:bg-gray-100/70 text-gray-500 hover:text-gray-700 !border-0 !bg-transparent"
                      />
                    </div>
                  </>
                )}


              </div>
            </div>

            
              </div>

              {/* Right Panel (AI Chat) - Fixed ~24rem width */}
              <div className={styles.desktopRightPanel}>
            {/* Chat Header */}
            {/* <div className="flex-shrink-0 p-4 border-b border-gray-100">
              <h2 className="text-lg font-semibold text-gray-900">AI Assistant</h2>
              <p className="text-sm text-gray-600 mt-1">
                Describe what you'd like to create or modify
              </p>
            </div> */}

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {state.messages.map((message, index) => (
                <div key={index}>
                  {message.role === 'user' ? (
                    /* User Message */
                    <div className="flex justify-end">
                      <div className="max-w-[80%] bg-violet-600 text-white rounded-2xl rounded-br-md px-4 py-3">
                        <div className="text-sm leading-relaxed whitespace-pre-wrap">
                          {message.content}
                        </div>
                        <div className="text-xs text-violet-100 mt-1 opacity-75">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* Assistant Message */
                    <div className="flex justify-start">
                      <div className="max-w-[80%] bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                        {message.type === 'plan' ? (
                          <div className="text-sm leading-relaxed">
                            {/* Implementation Plan Header */}
                            <div className="flex items-center space-x-2 mb-3">
                              <div className="w-4 h-4 text-orange-500">
                                <svg fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <span className="font-medium text-gray-900">Implementation Plan</span>
                            </div>

                            {/* Plan Content */}
                            <div className="text-gray-700 text-sm leading-relaxed">
                              {message.content.split('\n').map((line, lineIndex) => {
                                const trimmed = line.trim();
                                if (!trimmed) return null;

                                // Skip markdown headers and formatting
                                if (trimmed.startsWith('#') || trimmed.startsWith('**') || trimmed.startsWith('📋') || trimmed.startsWith('🎯')) {
                                  return null;
                                }

                                // Handle bullet points
                                if (trimmed.startsWith('•') || trimmed.startsWith('-')) {
                                  return (
                                    <div key={lineIndex} className="ml-4 mb-1">
                                      • {trimmed.replace(/^[•-]\s*/, '')}
                                    </div>
                                  );
                                }

                                // Handle numbered sections
                                if (/^\d+\.\s/.test(trimmed)) {
                                  return (
                                    <div key={lineIndex} className="font-medium text-gray-900 mt-3 mb-1">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                // Handle section headers
                                if (trimmed.endsWith(':') && trimmed.length < 30) {
                                  return (
                                    <div key={lineIndex} className="font-medium text-gray-900 mt-3 mb-1">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                // Regular text
                                if (trimmed.length > 10) {
                                  return (
                                    <div key={lineIndex} className="mb-2">
                                      {trimmed}
                                    </div>
                                  );
                                }

                                return null;
                              }).filter(Boolean)}
                            </div>
                          </div>
                        ) : (
                          <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
                            {message.content}
                          </div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          {message.timestamp.toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}

              {/* Typing Indicator */}
              {state.isGenerating && (
                <div className="flex justify-start">
                  <div className="bg-gray-50 border border-gray-200 rounded-2xl rounded-bl-md px-4 py-3">
                    <div className="flex items-center space-x-1">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                      </div>
                      <span className="text-xs text-gray-500 ml-2">AI is thinking...</span>
                    </div>
                  </div>
                </div>
              )}            </div>

            {/* Element Selector Controls - Prominent placement */}
            <div className="flex-shrink-0 p-4 border-t border-gray-100">
              <EditModeControls
                isEditModeActive={isEditModeActive}
                selectedElement={selectedElement}
                onToggleEditMode={() => elementSelectorManager.toggleEditMode()}
                onClearSelection={() => elementSelectorManager.clearSelection()}
                onExitEditMode={() => elementSelectorManager.exitEditMode()}
              />
            </div>

            {/* Chat Input */}
            <div className="flex-shrink-0 p-4 border-t border-gray-100">
              {/* Enhancement Preview */}
              {showEnhancement && enhancement && (
                <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center">
                      <svg className="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      <span className="text-sm font-medium text-blue-800">Enhanced Prompt</span>
                      <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                        enhancement.confidence === 'high' ? 'bg-green-100 text-green-800' :
                        enhancement.confidence === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {enhancement.confidence} confidence
                      </span>
                    </div>
                    <button onClick={handleCloseEnhancement} className="text-gray-400 hover:text-gray-600">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="text-sm text-gray-700 mb-2">
                    <strong>Analysis:</strong> {enhancement.analysisType}
                    {enhancement.gridChanges && (
                      <div className="mt-1">
                        <strong>Grid Changes:</strong> {enhancement.gridChanges}
                      </div>
                    )}
                  </div>

                  <div className="text-sm text-gray-600 mb-3 max-h-20 overflow-y-auto">
                    {enhancement.enhancedPrompt}
                  </div>

                  <div className="flex gap-2">
                    <button
                      onClick={handleUseEnhanced}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      Use Enhanced
                    </button>
                    <button
                      onClick={handleCloseEnhancement}
                      className="px-3 py-1 bg-gray-200 text-gray-700 text-sm rounded hover:bg-gray-300"
                    >
                      Use Original
                    </button>
                  </div>
                </div>
              )}

              {/* Enhancement Loading Indicator */}
              {isEnhancing && (
                <div className="flex items-center text-sm text-blue-600 mb-2">
                  <div className="w-3 h-3 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2"></div>
                  Analyzing prompt...
                </div>
              )}

              <PromptInput
                value={state.input}
                onChange={actions.setInput}
                onSubmit={(value) => {
                  if (value.trim() && !state.isGenerating) {
                    handleChatSubmit(value.trim());
                  }
                }}
                placeholder={state.isGenerating ? "AI is working..." : "Tell me what to change, specific and clear. One task at a time."}
                submitButtonText=""
                loadingText=""
                height="min-h-[100px] max-h-[200px]"
                theme="violet"
                isLoading={state.isGenerating}
                variant="chat"
                showSubmitButton={true}
                additionalContent={
                  <div className="flex justify-start mt-3">
                    <button
                      type="button"
                      onClick={() => setElementSelectorActive(!elementSelectorActive)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-xl text-sm font-medium transition-colors ${elementSelectorActive
                        ? 'bg-violet-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                      </svg>
                      <span>Selector</span>
                    </button>
                  </div>
                }
              />
            </div>
          </div>
            </div>
          )}
        </div>
      </div>
        {/* Modal Dialogs */}
        <ModalDialogs
          showPageCreationDialog={showPageCreationDialog}
          pendingPageCreation={pendingPageCreation}
          onConfirmPageCreation={confirmPageCreation}
          onCancelPageCreation={cancelPageCreation}
          showLinkingDialog={showLinkingDialog}
          onLinkPages={handleLinkPages}
          onCloseLinkingDialog={() => setShowLinkingDialog(false)}
          onDontShowLinkingAgain={() => {
            setShowLinkingDialog(false);
            setHasShownLinkingSuggestion(true);
          }}
          linkingProgress={linkingProgress}
          showImplementModal={false}
          selectedElement={null}
          customFunctionality=""
          isGeneratingIntent={false}
          onCloseImplementModal={() => { }}
          onSetCustomFunctionality={() => { }}
          onImplementChoice={() => { }}
          showDeleteConfirm={showDeleteConfirm}
          onConfirmDelete={confirmDelete}
          onCancelDelete={cancelDelete}
        />

        {/* Implementation Choice Modal */}
        <ImplementationModal
          showImplementModal={state.showImplementModal}
          selectedElement={state.selectedElement}
          customFunctionality={state.customFunctionality || ''}
          isGeneratingIntent={isGeneratingIntent}
          onCloseImplementModal={() => actions.setShowImplementModal(false)}
          onSetCustomFunctionality={actions.setCustomFunctionality}
          onImplementChoice={handleImplementChoice}
        />

      {/* VERSIONING DISABLED: Debug Panel for Versioning */}

      </div> {/* Close Main Container */}
    </ErrorBoundary>
  );
}

export default EditorPageV3Refactored;