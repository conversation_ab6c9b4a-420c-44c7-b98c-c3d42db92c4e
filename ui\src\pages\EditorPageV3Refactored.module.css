/**
 * Mobile Responsive Styles for EditorPageV3Refactored
 * 
 * This module provides mobile-specific styles and responsive enhancements
 * for the editor interface.
 */

/* ===== MOBILE RESPONSIVE LAYOUT ===== */

.mobileContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.headerBar {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  height: 64px; /* Fixed height */
  z-index: 40;
  flex-shrink: 0; /* Prevent shrinking */
  width: 100%; /* Full width */
}

.headerLeft {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.toggleButton {
  padding: 0.5rem;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 44px;
  min-width: 44px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toggleButton:hover {
  background: #f3f4f6;
}

.projectTitle {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

/* Mobile Tab Navigation */
.mobileTabNav {
  display: flex;
  background: #f3f4f6;
  border-radius: 0.5rem;
  padding: 0.25rem;
}

.mobileTab {
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  background: transparent;
}

.mobileTabActive {
  background: white;
  color: #111827;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.mobileTabInactive {
  color: #6b7280;
}

.mobileTabInactive:hover {
  color: #111827;
}

/* Mobile Content Areas */
.mobileContentArea {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 64px);
  overflow: hidden;
  width: 100%; /* Use 100% instead of 100vw to avoid overflow */
  min-height: 300px; /* Ensure minimum visibility */
  background-color: #f9fafb; /* Ensure background is visible */
}

.mobilePreviewTab {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  height: 100%;
}

.mobileChatTab {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  height: 100%;
}

/* Desktop Layout */
.desktopLayout {
  flex: 1; /* Take remaining space after header */
  display: flex;
  flex-direction: row;
  position: relative;
  overflow: hidden; /* Prevent any overflow issues */
  min-height: 0; /* Allow flex shrinking */
}

/* Desktop Panel Styles */
.desktopCenterPanel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f9fafb;
  height: 100%;
  overflow: hidden;
  min-width: 0; /* Allow flex shrinking */
}

.desktopRightPanel {
  width: 24rem; /* w-96 equivalent (384px) */
  background: white;
  border-left: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  height: 100%;
  flex-shrink: 0; /* Prevent shrinking */
  position: relative; /* Ensure proper positioning */
}

/* Ensure no gaps in desktop layout */
@media (min-width: 768px) {
  .desktopLayout {
    width: 100vw; /* Full viewport width */
  }

  .desktopRightPanel {
    margin-left: 0; /* Remove any default margins */
    border-right: none; /* No right border needed */
  }
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Hide mobile tabs on desktop */
@media (min-width: 768px) {
  .mobileTabNav {
    display: none;
  }
}

/* Hide desktop elements on mobile */
@media (max-width: 767px) {
  .projectTitle {
    display: none;
  }
}

/* ===== TOUCH-FRIENDLY ENHANCEMENTS ===== */

/* Ensure all interactive elements meet touch target requirements */
.touchTarget {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

/* Enhanced button styles for mobile */
.mobileButton {
  min-height: 44px;
  min-width: 44px;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  touch-action: manipulation;
  cursor: pointer;
}

.mobileButtonPrimary {
  background: #7c3aed;
  color: white;
  border: none;
}

.mobileButtonPrimary:hover {
  background: #6d28d9;
}

.mobileButtonSecondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
}

.mobileButtonSecondary:hover {
  background: #e5e7eb;
}

/* ===== ANIMATIONS AND TRANSITIONS ===== */

.slideIn {
  animation: slideInFromRight 0.3s ease-out;
}

.slideOut {
  animation: slideOutToRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.fadeIn {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== MOBILE CHAT ENHANCEMENTS ===== */

.mobileChatMessages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  gap: 1rem;
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.mobileChatInput {
  flex-shrink: 0;
  padding: 1rem;
  border-top: 1px solid #f3f4f6;
  background: white;
}

/* ===== DESKTOP CHAT ENHANCEMENTS ===== */

.desktopChatMessages {
  height: 24rem; /* 384px - Fixed height */
  overflow-y: auto;
  padding: 1rem;
  gap: 1rem;
  display: flex;
  flex-direction: column;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Responsive: Use flexible height on smaller screens */
@media (max-width: 767px) {
  .desktopChatMessages {
    height: auto;
    flex: 1;
    max-height: calc(100vh - 300px);
  }
}

/* Custom scrollbar for webkit browsers */
.desktopChatMessages::-webkit-scrollbar {
  width: 6px;
}

.desktopChatMessages::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.desktopChatMessages::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.desktopChatMessages::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* ===== MOBILE PREVIEW ENHANCEMENTS ===== */

.mobilePreviewContent {
  flex: 1;
  display: flex;
  overflow: hidden;
  padding: 1rem;
}

.mobilePreviewFrame {
  width: 100%;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  max-height: calc(100vh - 200px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */

/* Focus indicators for mobile */
.mobileTab:focus,
.toggleButton:focus,
.mobileButton:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .headerBar {
    border-bottom: 2px solid #000;
  }
  
  .mobileTabNav {
    border: 2px solid #000;
  }
  
  .toggleButton {
    border: 2px solid #000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .mobileTab,
  .toggleButton,
  .mobileButton {
    transition: none;
  }
  
  .slideIn,
  .slideOut,
  .fadeIn {
    animation: none;
  }
}

/* ===== SAFE AREA SUPPORT ===== */

/* Support for devices with notches */
.headerBar {
  padding-top: max(0.75rem, env(safe-area-inset-top));
  padding-left: max(1rem, env(safe-area-inset-left));
  padding-right: max(1rem, env(safe-area-inset-right));
}

.mobileContentArea {
  padding-bottom: env(safe-area-inset-bottom);
}

/* ===== LANDSCAPE MODE ADJUSTMENTS ===== */

@media (orientation: landscape) and (max-height: 500px) {
  .headerBar {
    padding: 0.5rem 1rem;
    min-height: 56px;
  }
  
  .mobileContentArea {
    height: calc(100vh - 56px);
  }
  
  .mobileTab {
    padding: 0.375rem 0.75rem;
    min-height: 40px;
  }
  
  .toggleButton {
    min-height: 40px;
    min-width: 40px;
    padding: 0.375rem;
  }
}
